/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6l4 2", key: "mmk7yg" }],
  ["path", { d: "M20 12v5", key: "12wsvk" }],
  ["path", { d: "M20 21h.01", key: "1p6o6n" }],
  ["path", { d: "M21.25 8.2A10 10 0 1 0 16 21.16", key: "17fp9f" }]
];
const ClockAlert = createLucideIcon("clock-alert", __iconNode);

export { __iconNode, ClockAlert as default };
//# sourceMappingURL=clock-alert.js.map
