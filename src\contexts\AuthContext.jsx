import { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext()

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  // Check for existing session on app load
  useEffect(() => {
    // Force logout to see new login page
    localStorage.removeItem('adminToken')
    localStorage.removeItem('adminUser')
    setIsAuthenticated(false)
    setUser(null)
    setLoading(false)
  }, [])

  const login = async (email, password) => {
    try {
      // For demo purposes, we'll use hardcoded admin credentials
      // In production, this would make an API call to your backend
      if (email === '<EMAIL>' && password === 'admin123') {
        const userData = {
          id: 1,
          email: '<EMAIL>',
          name: 'EliteMart Admin',
          role: 'super_admin',
          permissions: ['all']
        }
        
        const token = 'demo-admin-token-' + Date.now()
        
        localStorage.setItem('adminToken', token)
        localStorage.setItem('adminUser', JSON.stringify(userData))
        
        setIsAuthenticated(true)
        setUser(userData)
        
        return { success: true }
      } else {
        return { success: false, error: 'Invalid credentials' }
      }
    } catch (error) {
      return { success: false, error: 'Login failed' }
    }
  }

  const logout = () => {
    localStorage.removeItem('adminToken')
    localStorage.removeItem('adminUser')
    setIsAuthenticated(false)
    setUser(null)
  }

  const value = {
    isAuthenticated,
    user,
    login,
    logout,
    loading
  }

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  )
}
