/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6l-2 4", key: "1095bu" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const Clock7 = createLucideIcon("clock-7", __iconNode);

export { __iconNode, Clock7 as default };
//# sourceMappingURL=clock-7.js.map
