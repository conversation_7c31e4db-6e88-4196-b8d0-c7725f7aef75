# Setup Android Environment Variables for React Native
Write-Host "Setting up Android environment variables..." -ForegroundColor Green

# Set ANDROID_HOME
$androidHome = "C:\Users\<USER>\AppData\Local\Android\Sdk"
[Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidHome, "User")

# Add Android SDK tools to PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$platformTools = "$androidHome\platform-tools"
$cmdlineTools = "$androidHome\cmdline-tools\latest\bin"

if ($currentPath -notlike "*$platformTools*") {
    $newPath = "$currentPath;$platformTools"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "Added platform-tools to PATH" -ForegroundColor Yellow
}

if ($currentPath -notlike "*$cmdlineTools*") {
    $newPath = [Environment]::GetEnvironmentVariable("PATH", "User") + ";$cmdlineTools"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "Added cmdline-tools to PATH" -ForegroundColor Yellow
}

Write-Host "Environment variables set successfully!" -ForegroundColor Green
Write-Host "Please restart your terminal or VS Code for changes to take effect." -ForegroundColor Cyan

# Display current environment
Write-Host "`nCurrent Environment:" -ForegroundColor Blue
Write-Host "ANDROID_HOME: $([Environment]::GetEnvironmentVariable('ANDROID_HOME', 'User'))"
Write-Host "PATH includes platform-tools: $($([Environment]::GetEnvironmentVariable('PATH', 'User')) -like '*platform-tools*')"
