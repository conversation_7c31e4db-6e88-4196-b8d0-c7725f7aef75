import { useState, useEffect } from 'react'

function OrderManagement() {
  const [orders, setOrders] = useState([])
  const [filteredOrders, setFilteredOrders] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [selectedOrder, setSelectedOrder] = useState(null)
  const [showOrderModal, setShowOrderModal] = useState(false)
  const [loading, setLoading] = useState(true)

  // Mock order data - in production, this would come from your API
  useEffect(() => {
    const mockOrders = [
      {
        id: 'ORD-001234',
        customerName: 'Priya Sharma',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 98765 43210',
        orderDate: '2024-06-28',
        status: 'pending',
        paymentStatus: 'paid',
        paymentMethod: 'UPI',
        totalAmount: 134900,
        shippingAddress: {
          street: '123 MG Road',
          city: 'Mumbai',
          state: 'Maharashtra',
          pincode: '400001',
          country: 'India'
        },
        items: [
          {
            id: 1,
            name: 'iPhone 15 Pro Max',
            sku: 'IPH15PM-256-TB',
            quantity: 1,
            price: 134900,
            image: '📱'
          }
        ],
        timeline: [
          { status: 'placed', date: '2024-06-28T10:30:00Z', description: 'Order placed successfully' },
          { status: 'confirmed', date: '2024-06-28T11:00:00Z', description: 'Order confirmed by seller' }
        ]
      },
      {
        id: 'ORD-001235',
        customerName: 'Rahul Kumar',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 87654 32109',
        orderDate: '2024-06-27',
        status: 'processing',
        paymentStatus: 'paid',
        paymentMethod: 'Credit Card',
        totalAmount: 154989,
        shippingAddress: {
          street: '456 CP Market',
          city: 'Delhi',
          state: 'Delhi',
          pincode: '110001',
          country: 'India'
        },
        items: [
          {
            id: 2,
            name: 'Samsung Galaxy S24 Ultra',
            sku: 'SGS24U-512-BK',
            quantity: 1,
            price: 124999,
            image: '📱'
          },
          {
            id: 3,
            name: 'Sony WH-1000XM5 Headphones',
            sku: 'SNYWH1000XM5-BK',
            quantity: 1,
            price: 29990,
            image: '🎧'
          }
        ],
        timeline: [
          { status: 'placed', date: '2024-06-27T14:20:00Z', description: 'Order placed successfully' },
          { status: 'confirmed', date: '2024-06-27T15:00:00Z', description: 'Order confirmed by seller' },
          { status: 'processing', date: '2024-06-28T09:00:00Z', description: 'Order is being processed' }
        ]
      },
      {
        id: 'ORD-001236',
        customerName: 'Anita Patel',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 76543 21098',
        orderDate: '2024-06-26',
        status: 'shipped',
        paymentStatus: 'paid',
        paymentMethod: 'Net Banking',
        totalAmount: 114900,
        trackingNumber: 'TRK123456789',
        shippingAddress: {
          street: '789 SG Highway',
          city: 'Ahmedabad',
          state: 'Gujarat',
          pincode: '380001',
          country: 'India'
        },
        items: [
          {
            id: 4,
            name: 'MacBook Air M3',
            sku: 'MBA13M3-256-SG',
            quantity: 1,
            price: 114900,
            image: '💻'
          }
        ],
        timeline: [
          { status: 'placed', date: '2024-06-26T16:45:00Z', description: 'Order placed successfully' },
          { status: 'confirmed', date: '2024-06-26T17:00:00Z', description: 'Order confirmed by seller' },
          { status: 'processing', date: '2024-06-27T10:00:00Z', description: 'Order is being processed' },
          { status: 'shipped', date: '2024-06-28T08:00:00Z', description: 'Order shipped with tracking number TRK123456789' }
        ]
      },
      {
        id: 'ORD-001237',
        customerName: 'Vikram Singh',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 65432 10987',
        orderDate: '2024-06-25',
        status: 'delivered',
        paymentStatus: 'paid',
        paymentMethod: 'UPI',
        totalAmount: 12995,
        deliveredDate: '2024-06-27',
        shippingAddress: {
          street: '321 MI Road',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302001',
          country: 'India'
        },
        items: [
          {
            id: 5,
            name: 'Nike Air Max 270',
            sku: 'NAM270-42-BW',
            quantity: 1,
            price: 12995,
            image: '👟'
          }
        ],
        timeline: [
          { status: 'placed', date: '2024-06-25T12:30:00Z', description: 'Order placed successfully' },
          { status: 'confirmed', date: '2024-06-25T13:00:00Z', description: 'Order confirmed by seller' },
          { status: 'processing', date: '2024-06-26T09:00:00Z', description: 'Order is being processed' },
          { status: 'shipped', date: '2024-06-26T18:00:00Z', description: 'Order shipped' },
          { status: 'delivered', date: '2024-06-27T14:30:00Z', description: 'Order delivered successfully' }
        ]
      },
      {
        id: 'ORD-001238',
        customerName: 'Sneha Reddy',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 54321 09876',
        orderDate: '2024-06-24',
        status: 'cancelled',
        paymentStatus: 'refunded',
        paymentMethod: 'Credit Card',
        totalAmount: 29990,
        cancelReason: 'Customer requested cancellation',
        shippingAddress: {
          street: '654 Banjara Hills',
          city: 'Hyderabad',
          state: 'Telangana',
          pincode: '500034',
          country: 'India'
        },
        items: [
          {
            id: 3,
            name: 'Sony WH-1000XM5 Headphones',
            sku: 'SNYWH1000XM5-BK',
            quantity: 1,
            price: 29990,
            image: '🎧'
          }
        ],
        timeline: [
          { status: 'placed', date: '2024-06-24T11:15:00Z', description: 'Order placed successfully' },
          { status: 'confirmed', date: '2024-06-24T11:30:00Z', description: 'Order confirmed by seller' },
          { status: 'cancelled', date: '2024-06-24T15:00:00Z', description: 'Order cancelled - Customer requested cancellation' }
        ]
      }
    ]

    setTimeout(() => {
      setOrders(mockOrders)
      setFilteredOrders(mockOrders)
      setLoading(false)
    }, 1000)
  }, [])

  // Filter orders based on search, status, and date
  useEffect(() => {
    let filtered = orders

    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerEmail.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    if (dateFilter !== 'all') {
      const today = new Date()
      const filterDate = new Date()

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          filtered = filtered.filter(order => new Date(order.orderDate) >= filterDate)
          break
        case 'week':
          filterDate.setDate(today.getDate() - 7)
          filtered = filtered.filter(order => new Date(order.orderDate) >= filterDate)
          break
        case 'month':
          filterDate.setMonth(today.getMonth() - 1)
          filtered = filtered.filter(order => new Date(order.orderDate) >= filterDate)
          break
      }
    }

    setFilteredOrders(filtered)
  }, [orders, searchTerm, statusFilter, dateFilter])

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-blue-100 text-blue-800'
      case 'processing': return 'bg-purple-100 text-purple-800'
      case 'shipped': return 'bg-indigo-100 text-indigo-800'
      case 'delivered': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return '⏳'
      case 'confirmed': return '✅'
      case 'processing': return '⚙️'
      case 'shipped': return '🚚'
      case 'delivered': return '📦'
      case 'cancelled': return '❌'
      default: return '❓'
    }
  }

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'refunded': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleStatusUpdate = (orderId, newStatus) => {
    setOrders(orders.map(order => {
      if (order.id === orderId) {
        const updatedOrder = { ...order, status: newStatus }
        // Add timeline entry
        updatedOrder.timeline.push({
          status: newStatus,
          date: new Date().toISOString(),
          description: `Order status updated to ${newStatus}`
        })
        return updatedOrder
      }
      return order
    }))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl p-8 text-white shadow-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-3xl mr-4">
              🛒
            </div>
            <div>
              <h1 className="text-3xl font-bold mb-2">Order Management</h1>
              <p className="text-orange-100 text-lg">Track and manage customer orders and fulfillment</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-orange-100 text-sm">Total Orders</p>
            <p className="text-3xl font-bold">{orders.length}</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Pending Orders</p>
              <p className="text-2xl font-bold text-yellow-600">
                {orders.filter(o => o.status === 'pending').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center text-2xl">
              ⏳
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Processing</p>
              <p className="text-2xl font-bold text-purple-600">
                {orders.filter(o => o.status === 'processing').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center text-2xl">
              ⚙️
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Shipped</p>
              <p className="text-2xl font-bold text-indigo-600">
                {orders.filter(o => o.status === 'shipped').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center text-2xl">
              🚚
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Delivered</p>
              <p className="text-2xl font-bold text-green-600">
                {orders.filter(o => o.status === 'delivered').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center text-2xl">
              📦
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Total Revenue</p>
              <p className="text-2xl font-bold text-orange-600">
                ₹{orders.filter(o => o.status !== 'cancelled').reduce((sum, o) => sum + o.totalAmount, 0).toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center text-2xl">
              💰
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card p-6">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <input
                type="text"
                placeholder="Search orders by ID, customer name, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                🔍
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input-field"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>

            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="input-field"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
            </select>

            <button className="btn-primary">
              Export Orders
            </button>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order Details
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Items
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50 transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{order.id}</div>
                      <div className="text-sm text-gray-500">{new Date(order.orderDate).toLocaleDateString()}</div>
                      {order.trackingNumber && (
                        <div className="text-xs text-blue-600 font-mono">{order.trackingNumber}</div>
                      )}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{order.customerName}</div>
                      <div className="text-sm text-gray-500">{order.customerEmail}</div>
                      <div className="text-sm text-gray-500">{order.customerPhone}</div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex -space-x-2">
                        {order.items.slice(0, 3).map((item, index) => (
                          <div key={index} className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center text-white text-sm border-2 border-white">
                            {item.image}
                          </div>
                        ))}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm text-gray-900">{order.items.length} item{order.items.length > 1 ? 's' : ''}</div>
                        <div className="text-sm text-gray-500">
                          {order.items.reduce((sum, item) => sum + item.quantity, 0)} units
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">₹{order.totalAmount.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">{order.paymentMethod}</div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      <span className="mr-1">{getStatusIcon(order.status)}</span>
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(order.paymentStatus)}`}>
                      {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => {
                        setSelectedOrder(order)
                        setShowOrderModal(true)
                      }}
                      className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-lg transition-colors duration-200"
                    >
                      View
                    </button>

                    {order.status === 'pending' && (
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'confirmed')}
                        className="text-green-600 hover:text-green-900 bg-green-50 hover:bg-green-100 px-3 py-1 rounded-lg transition-colors duration-200"
                      >
                        Confirm
                      </button>
                    )}

                    {order.status === 'confirmed' && (
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'processing')}
                        className="text-purple-600 hover:text-purple-900 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-lg transition-colors duration-200"
                      >
                        Process
                      </button>
                    )}

                    {order.status === 'processing' && (
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'shipped')}
                        className="text-indigo-600 hover:text-indigo-900 bg-indigo-50 hover:bg-indigo-100 px-3 py-1 rounded-lg transition-colors duration-200"
                      >
                        Ship
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🛒</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>

      {/* Order Detail Modal */}
      {showOrderModal && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Order Details</h2>
                  <p className="text-gray-600">{selectedOrder.id}</p>
                </div>
                <button
                  onClick={() => setShowOrderModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Order Status and Timeline */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Status</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Current Status:</span>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedOrder.status)}`}>
                        <span className="mr-1">{getStatusIcon(selectedOrder.status)}</span>
                        {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Payment Status:</span>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusColor(selectedOrder.paymentStatus)}`}>
                        {selectedOrder.paymentStatus.charAt(0).toUpperCase() + selectedOrder.paymentStatus.slice(1)}
                      </span>
                    </div>
                    {selectedOrder.trackingNumber && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Tracking Number:</span>
                        <span className="font-mono text-blue-600">{selectedOrder.trackingNumber}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Timeline</h3>
                  <div className="space-y-3">
                    {selectedOrder.timeline.map((event, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className={`w-3 h-3 rounded-full mt-1 ${
                          event.status === selectedOrder.status ? 'bg-orange-500' : 'bg-gray-300'
                        }`}></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{event.description}</p>
                          <p className="text-xs text-gray-500">{new Date(event.date).toLocaleString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Customer Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">Contact Details</h4>
                    <div className="space-y-2 text-sm">
                      <p><span className="text-gray-600">Name:</span> {selectedOrder.customerName}</p>
                      <p><span className="text-gray-600">Email:</span> {selectedOrder.customerEmail}</p>
                      <p><span className="text-gray-600">Phone:</span> {selectedOrder.customerPhone}</p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">Shipping Address</h4>
                    <div className="text-sm text-gray-900">
                      <p>{selectedOrder.shippingAddress.street}</p>
                      <p>{selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.state}</p>
                      <p>{selectedOrder.shippingAddress.pincode}, {selectedOrder.shippingAddress.country}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                <div className="space-y-3">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center text-white text-xl">
                          {item.image}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{item.name}</h4>
                          <p className="text-sm text-gray-600">SKU: {item.sku}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">₹{item.price.toLocaleString()}</p>
                        <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Payment Summary */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="text-gray-900">₹{selectedOrder.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shipping:</span>
                      <span className="text-gray-900">Free</span>
                    </div>
                    <div className="border-t border-gray-200 pt-2">
                      <div className="flex justify-between font-semibold">
                        <span className="text-gray-900">Total:</span>
                        <span className="text-gray-900">₹{selectedOrder.totalAmount.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="mt-2">
                      <span className="text-sm text-gray-600">Payment Method: {selectedOrder.paymentMethod}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t border-gray-200">
                {selectedOrder.status === 'pending' && (
                  <>
                    <button
                      onClick={() => {
                        handleStatusUpdate(selectedOrder.id, 'confirmed')
                        setShowOrderModal(false)
                      }}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200"
                    >
                      Confirm Order
                    </button>
                    <button
                      onClick={() => {
                        handleStatusUpdate(selectedOrder.id, 'cancelled')
                        setShowOrderModal(false)
                      }}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors duration-200"
                    >
                      Cancel Order
                    </button>
                  </>
                )}

                {selectedOrder.status === 'confirmed' && (
                  <button
                    onClick={() => {
                      handleStatusUpdate(selectedOrder.id, 'processing')
                      setShowOrderModal(false)
                    }}
                    className="bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors duration-200"
                  >
                    Start Processing
                  </button>
                )}

                {selectedOrder.status === 'processing' && (
                  <button
                    onClick={() => {
                      handleStatusUpdate(selectedOrder.id, 'shipped')
                      setShowOrderModal(false)
                    }}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors duration-200"
                  >
                    Mark as Shipped
                  </button>
                )}

                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200">
                  Print Invoice
                </button>

                <button className="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 transition-colors duration-200">
                  Send Update to Customer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default OrderManagement
