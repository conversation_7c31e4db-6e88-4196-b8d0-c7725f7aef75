import React, { useState, useEffect } from 'react'
import { useTheme } from '../contexts/ThemeContext'
import { useParams, Link } from 'react-router-dom'

function EditProduct() {
  const { isDarkMode } = useTheme()
  const { id } = useParams()
  const [formData, setFormData] = useState({
    productId: '',
    productName: '',
    category: '',
    subCategory: '',
    seller: '',
    brandName: '',
    tags: [],
    unit: 'pcs',
    mrp: '',
    sellingPrice: '',
    stock: '',
    discount: '',
    status: 'Active',
    returnable: 'Yes',
    cod: 'Yes',
    description: ''
  })
  const [images, setImages] = useState([])
  const [tagInput, setTagInput] = useState('')
  const [lastModified, setLastModified] = useState(null)
  const [modifiedFields, setModifiedFields] = useState(new Set())

  // Sample product data for editing
  useEffect(() => {
    // In a real app, you would fetch the product data by ID
    const sampleProduct = {
      productId: 'PRD001',
      productName: 'Fresh Organic Tomatoes',
      category: 'Fresh Vegetables',
      subCategory: 'Tomatoes',
      seller: 'Green Valley Farms',
      brandName: 'Organic Fresh',
      tags: ['organic', 'fresh', 'vegetables'],
      unit: 'kg',
      mrp: '80',
      sellingPrice: '65',
      stock: '150',
      discount: '18.75',
      status: 'Active',
      returnable: 'Yes',
      cod: 'Yes',
      description: 'Fresh organic tomatoes grown without pesticides. Rich in vitamins and perfect for cooking.'
    }
    
    setFormData(sampleProduct)
    setLastModified(new Date('2025-07-08T10:30:00'))
  }, [id])

  // Handle form input changes and track modifications
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setModifiedFields(prev => new Set([...prev, field]))
  }

  // Handle tag addition
  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setModifiedFields(prev => new Set([...prev, 'tags']))
      setTagInput('')
    }
  }

  // Handle tag removal
  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
    setModifiedFields(prev => new Set([...prev, 'tags']))
  }

  // Handle image upload
  const handleImageUpload = (event) => {
    const files = Array.from(event.target.files)
    setImages(prev => [...prev, ...files])
    setModifiedFields(prev => new Set([...prev, 'images']))
  }

  // Remove image
  const removeImage = (index) => {
    setImages(prev => prev.filter((_, i) => i !== index))
    setModifiedFields(prev => new Set([...prev, 'images']))
  }

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('Updated Product Data:', formData)
    console.log('Modified Fields:', Array.from(modifiedFields))
    console.log('Images:', images)
    // Here you would typically send the updated data to your API
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="p-6">
        {/* Page Title & Breadcrumb */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <nav className={`text-sm mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              <span>Dashboard</span>
              <span className="mx-2">/</span>
              <span>Products</span>
              <span className="mx-2">/</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Edit Product</span>
            </nav>
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Edit Product: {formData.productName}
            </h1>
            {lastModified && (
              <p className={`text-sm mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Last modified: {lastModified.toLocaleString('en-IN')}
              </p>
            )}
          </div>
          
          {/* Modified Fields Indicator */}
          {modifiedFields.size > 0 && (
            <div className={`px-4 py-2 rounded-lg ${isDarkMode ? 'bg-yellow-900 border border-yellow-700' : 'bg-yellow-100 border border-yellow-300'}`}>
              <span className={`text-sm ${isDarkMode ? 'text-yellow-300' : 'text-yellow-800'}`}>
                {modifiedFields.size} field(s) modified
              </span>
            </div>
          )}
        </div>

        {/* Edit Product Form */}
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Section */}
            <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
              <h2 className={`text-xl font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Basic Information
              </h2>

              {/* Product ID (Disabled) */}
              <div className="mb-6">
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Product ID
                </label>
                <input
                  type="text"
                  value={formData.productId}
                  disabled
                  className={`w-full px-4 py-3 rounded-lg border ${
                    isDarkMode ? 'bg-gray-600 border-gray-500 text-gray-400' : 'bg-gray-100 border-gray-300 text-gray-500'
                  } cursor-not-allowed`}
                />
                <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Product ID cannot be modified
                </p>
              </div>

              {/* Product Name */}
              <div className="mb-6">
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Product Name *
                  {modifiedFields.has('productName') && <span className="text-yellow-500 ml-1">●</span>}
                </label>
                <input
                  type="text"
                  value={formData.productName}
                  onChange={(e) => handleInputChange('productName', e.target.value)}
                  placeholder="Enter product name"
                  className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    modifiedFields.has('productName') 
                      ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20' 
                      : isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                  required
                />
              </div>

              {/* Current Images Display */}
              <div className="mb-6">
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Current Images
                  {modifiedFields.has('images') && <span className="text-yellow-500 ml-1">●</span>}
                </label>
                <div className="grid grid-cols-3 gap-4 mb-4">
                  {/* Sample current images */}
                  <div className="relative">
                    <div className="w-full h-24 bg-gray-200 rounded-lg flex items-center justify-center text-4xl">
                      🍅
                    </div>
                    <button
                      type="button"
                      className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                  </div>
                </div>

                {/* Upload New Images */}
                <div className={`border-2 border-dashed rounded-lg p-4 text-center ${
                  isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'
                }`}>
                  <div className="text-2xl mb-2">📷</div>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                    Add more images
                  </p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload-edit"
                  />
                  <label
                    htmlFor="image-upload-edit"
                    className="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded cursor-pointer transition-all duration-200 text-sm"
                  >
                    Choose Images
                  </label>
                </div>

                {/* New Image Preview */}
                {images.length > 0 && (
                  <div className="mt-4 grid grid-cols-3 gap-4">
                    {images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`New ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg border-2 border-green-400"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                        <span className="absolute bottom-1 left-1 bg-green-500 text-white text-xs px-1 rounded">NEW</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Category & Sub-category */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Category *
                    {modifiedFields.has('category') && <span className="text-yellow-500 ml-1">●</span>}
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                      modifiedFields.has('category') 
                        ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20' 
                        : isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    required
                  >
                    <option value="">Select Category</option>
                    <option value="Fresh Vegetables">Fresh Vegetables</option>
                    <option value="Fresh Fruits">Fresh Fruits</option>
                    <option value="Dairy Products">Dairy Products</option>
                    <option value="Bakery Items">Bakery Items</option>
                    <option value="Beverages">Beverages</option>
                    <option value="Snacks">Snacks</option>
                    <option value="Meat & Seafood">Meat & Seafood</option>
                    <option value="Personal Care">Personal Care</option>
                    <option value="Pantry Staples">Pantry Staples</option>
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Sub-category
                    {modifiedFields.has('subCategory') && <span className="text-yellow-500 ml-1">●</span>}
                  </label>
                  <input
                    type="text"
                    value={formData.subCategory}
                    onChange={(e) => handleInputChange('subCategory', e.target.value)}
                    placeholder="Enter sub-category"
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                      modifiedFields.has('subCategory') 
                        ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20' 
                        : isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                </div>
              </div>
            </div>

            {/* Right Section */}
            <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
              <h2 className={`text-xl font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Pricing & Inventory
              </h2>

              {/* MRP & Selling Price */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    MRP (₹) *
                    {modifiedFields.has('mrp') && <span className="text-yellow-500 ml-1">●</span>}
                  </label>
                  <input
                    type="number"
                    value={formData.mrp}
                    onChange={(e) => handleInputChange('mrp', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                      modifiedFields.has('mrp')
                        ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20'
                        : isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    required
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Selling Price (₹) *
                    {modifiedFields.has('sellingPrice') && <span className="text-yellow-500 ml-1">●</span>}
                  </label>
                  <input
                    type="number"
                    value={formData.sellingPrice}
                    onChange={(e) => handleInputChange('sellingPrice', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                      modifiedFields.has('sellingPrice')
                        ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20'
                        : isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    required
                  />
                </div>
              </div>

              {/* Stock & Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Stock Quantity *
                    {modifiedFields.has('stock') && <span className="text-yellow-500 ml-1">●</span>}
                  </label>
                  <input
                    type="number"
                    value={formData.stock}
                    onChange={(e) => handleInputChange('stock', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                      modifiedFields.has('stock')
                        ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20'
                        : isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    required
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Status
                    {modifiedFields.has('status') && <span className="text-yellow-500 ml-1">●</span>}
                  </label>
                  <div className="flex items-center space-x-6 mt-3">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        value="Active"
                        checked={formData.status === 'Active'}
                        onChange={(e) => handleInputChange('status', e.target.value)}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 border-2 rounded-full flex items-center justify-center ${
                        formData.status === 'Active' ? 'border-green-500' : 'border-gray-400'
                      }`}>
                        {formData.status === 'Active' && <div className="w-2 h-2 bg-green-500 rounded-full"></div>}
                      </div>
                      <span className={`ml-2 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Active</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        value="Inactive"
                        checked={formData.status === 'Inactive'}
                        onChange={(e) => handleInputChange('status', e.target.value)}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 border-2 rounded-full flex items-center justify-center ${
                        formData.status === 'Inactive' ? 'border-red-500' : 'border-gray-400'
                      }`}>
                        {formData.status === 'Inactive' && <div className="w-2 h-2 bg-red-500 rounded-full"></div>}
                      </div>
                      <span className={`ml-2 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Inactive</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className={`rounded-xl p-6 mt-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {modifiedFields.size > 0 && (
                  <div className={`px-3 py-1 rounded-full text-sm ${isDarkMode ? 'bg-yellow-900 text-yellow-300' : 'bg-yellow-100 text-yellow-800'}`}>
                    {modifiedFields.size} unsaved change(s)
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-4">
                <Link
                  to="/products"
                  className={`px-6 py-3 rounded-lg border transition-all duration-200 ${
                    isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={modifiedFields.size === 0}
                  className={`px-6 py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 ${
                    modifiedFields.size > 0
                      ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <span>💾</span>
                  <span>Update Product</span>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EditProduct
