/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6l1.56.78", key: "14ed3g" }],
  ["path", { d: "M13.227 21.925a10 10 0 1 1 8.767-9.588", key: "jwkls1" }],
  ["path", { d: "m14 18 4-4 4 4", key: "ftkppy" }],
  ["path", { d: "M18 22v-8", key: "su0gjh" }]
];
const ClockArrowUp = createLucideIcon("clock-arrow-up", __iconNode);

export { __iconNode, ClockArrowUp as default };
//# sourceMappingURL=clock-arrow-up.js.map
