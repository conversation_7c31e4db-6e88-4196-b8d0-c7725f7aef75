import { useState, useEffect } from 'react'

function ProductManagement() {
  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [selectedProduct, setSelectedProduct] = useState(null)
  const [showProductModal, setShowProductModal] = useState(false)
  const [showAddModal, setShowAddModal] = useState(false)
  const [loading, setLoading] = useState(true)

  // Mock product data - in production, this would come from your API
  useEffect(() => {
    const mockProducts = [
      {
        id: 1,
        name: 'iPhone 15 Pro Max',
        description: 'Latest iPhone with advanced camera system and A17 Pro chip',
        price: 134900,
        originalPrice: 139900,
        category: 'Electronics',
        subcategory: 'Smartphones',
        brand: 'Apple',
        sku: 'IPH15PM-256-TB',
        quantity: 25,
        lowStockThreshold: 10,
        status: 'active',
        featured: true,
        images: ['📱', '📷', '🔋'],
        attributes: {
          color: 'Titanium Blue',
          storage: '256GB',
          warranty: '1 Year'
        },
        createdDate: '2024-06-01',
        lastUpdated: '2024-06-25',
        sales: 45,
        rating: 4.8,
        reviews: 128
      },
      {
        id: 2,
        name: 'Samsung Galaxy S24 Ultra',
        description: 'Premium Android smartphone with S Pen and advanced AI features',
        price: 124999,
        originalPrice: 129999,
        category: 'Electronics',
        subcategory: 'Smartphones',
        brand: 'Samsung',
        sku: 'SGS24U-512-BK',
        quantity: 18,
        lowStockThreshold: 15,
        status: 'active',
        featured: true,
        images: ['📱', '✏️', '🤖'],
        attributes: {
          color: 'Phantom Black',
          storage: '512GB',
          warranty: '1 Year'
        },
        createdDate: '2024-05-15',
        lastUpdated: '2024-06-20',
        sales: 32,
        rating: 4.7,
        reviews: 89
      },
      {
        id: 3,
        name: 'Sony WH-1000XM5 Headphones',
        description: 'Industry-leading noise canceling wireless headphones',
        price: 29990,
        originalPrice: 34990,
        category: 'Electronics',
        subcategory: 'Audio',
        brand: 'Sony',
        sku: 'SNYWH1000XM5-BK',
        quantity: 5,
        lowStockThreshold: 10,
        status: 'active',
        featured: false,
        images: ['🎧', '🔇', '🎵'],
        attributes: {
          color: 'Black',
          connectivity: 'Bluetooth 5.2',
          warranty: '1 Year'
        },
        createdDate: '2024-04-20',
        lastUpdated: '2024-06-15',
        sales: 67,
        rating: 4.9,
        reviews: 234
      },
      {
        id: 4,
        name: 'MacBook Air M3',
        description: '13-inch laptop with M3 chip, perfect for everyday computing',
        price: 114900,
        originalPrice: 119900,
        category: 'Electronics',
        subcategory: 'Laptops',
        brand: 'Apple',
        sku: 'MBA13M3-256-SG',
        quantity: 12,
        lowStockThreshold: 8,
        status: 'active',
        featured: true,
        images: ['💻', '⚡', '🍃'],
        attributes: {
          color: 'Space Gray',
          storage: '256GB SSD',
          ram: '8GB',
          warranty: '1 Year'
        },
        createdDate: '2024-03-10',
        lastUpdated: '2024-06-18',
        sales: 28,
        rating: 4.8,
        reviews: 156
      },
      {
        id: 5,
        name: 'Nike Air Max 270',
        description: 'Comfortable lifestyle sneakers with Max Air cushioning',
        price: 12995,
        originalPrice: 14995,
        category: 'Fashion',
        subcategory: 'Footwear',
        brand: 'Nike',
        sku: 'NAM270-42-BW',
        quantity: 0,
        lowStockThreshold: 20,
        status: 'out_of_stock',
        featured: false,
        images: ['👟', '💨', '🏃'],
        attributes: {
          color: 'Black/White',
          size: '42',
          material: 'Mesh/Synthetic'
        },
        createdDate: '2024-02-28',
        lastUpdated: '2024-06-22',
        sales: 89,
        rating: 4.6,
        reviews: 267
      }
    ]

    setTimeout(() => {
      setProducts(mockProducts)
      setFilteredProducts(mockProducts)
      setLoading(false)
    }, 1000)
  }, [])

  // Filter products based on search, category, and status
  useEffect(() => {
    let filtered = products

    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(product => product.category === categoryFilter)
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(product => product.status === statusFilter)
    }

    setFilteredProducts(filtered)
  }, [products, searchTerm, categoryFilter, statusFilter])

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'out_of_stock': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return '✅'
      case 'inactive': return '⏸️'
      case 'out_of_stock': return '❌'
      default: return '❓'
    }
  }

  const getStockStatus = (product) => {
    if (product.quantity === 0) return { text: 'Out of Stock', color: 'text-red-600' }
    if (product.quantity <= product.lowStockThreshold) return { text: 'Low Stock', color: 'text-yellow-600' }
    return { text: 'In Stock', color: 'text-green-600' }
  }

  const handleStatusChange = (productId, newStatus) => {
    setProducts(products.map(product =>
      product.id === productId ? { ...product, status: newStatus } : product
    ))
  }

  const handleFeaturedToggle = (productId) => {
    setProducts(products.map(product =>
      product.id === productId ? { ...product, featured: !product.featured } : product
    ))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl p-8 text-white shadow-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-3xl mr-4">
              📦
            </div>
            <div>
              <h1 className="text-3xl font-bold mb-2">Product Management</h1>
              <p className="text-purple-100 text-lg">Manage your product catalog, inventory, and pricing</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-purple-100 text-sm">Total Products</p>
            <p className="text-3xl font-bold">{products.length}</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Active Products</p>
              <p className="text-2xl font-bold text-green-600">
                {products.filter(p => p.status === 'active').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center text-2xl">
              ✅
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Out of Stock</p>
              <p className="text-2xl font-bold text-red-600">
                {products.filter(p => p.quantity === 0).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-2xl">
              ❌
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Low Stock</p>
              <p className="text-2xl font-bold text-yellow-600">
                {products.filter(p => p.quantity > 0 && p.quantity <= p.lowStockThreshold).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center text-2xl">
              ⚠️
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Featured Products</p>
              <p className="text-2xl font-bold text-purple-600">
                {products.filter(p => p.featured).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center text-2xl">
              ⭐
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card p-6">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <input
                type="text"
                placeholder="Search products by name, brand, or SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                🔍
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="input-field"
            >
              <option value="all">All Categories</option>
              <option value="Electronics">Electronics</option>
              <option value="Fashion">Fashion</option>
              <option value="Home">Home</option>
              <option value="Sports">Sports</option>
            </select>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input-field"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>

            <button
              onClick={() => setShowAddModal(true)}
              className="btn-primary"
            >
              Add Product
            </button>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product) => {
                const stockStatus = getStockStatus(product)
                return (
                  <tr key={product.id} className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center text-white text-xl mr-4">
                          {product.images[0]}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 flex items-center">
                            {product.name}
                            {product.featured && <span className="ml-2 text-yellow-500">⭐</span>}
                          </div>
                          <div className="text-sm text-gray-500">{product.brand} • {product.sku}</div>
                        </div>
                      </div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{product.category}</div>
                      <div className="text-sm text-gray-500">{product.subcategory}</div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">₹{product.price.toLocaleString()}</div>
                      {product.originalPrice > product.price && (
                        <div className="text-sm text-gray-500 line-through">₹{product.originalPrice.toLocaleString()}</div>
                      )}
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{product.quantity} units</div>
                      <div className={`text-sm ${stockStatus.color}`}>{stockStatus.text}</div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                        <span className="mr-1">{getStatusIcon(product.status)}</span>
                        {product.status.replace('_', ' ').charAt(0).toUpperCase() + product.status.replace('_', ' ').slice(1)}
                      </span>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>Sales: {product.sales}</div>
                      <div className="text-gray-500">⭐ {product.rating} ({product.reviews})</div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => {
                          setSelectedProduct(product)
                          setShowProductModal(true)
                        }}
                        className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-lg transition-colors duration-200"
                      >
                        View
                      </button>

                      <button
                        onClick={() => handleFeaturedToggle(product.id)}
                        className={`px-3 py-1 rounded-lg transition-colors duration-200 ${
                          product.featured
                            ? 'text-yellow-600 hover:text-yellow-900 bg-yellow-50 hover:bg-yellow-100'
                            : 'text-gray-600 hover:text-gray-900 bg-gray-50 hover:bg-gray-100'
                        }`}
                      >
                        {product.featured ? 'Unfeature' : 'Feature'}
                      </button>

                      {product.status === 'active' && (
                        <button
                          onClick={() => handleStatusChange(product.id, 'inactive')}
                          className="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 px-3 py-1 rounded-lg transition-colors duration-200"
                        >
                          Deactivate
                        </button>
                      )}

                      {product.status === 'inactive' && (
                        <button
                          onClick={() => handleStatusChange(product.id, 'active')}
                          className="text-green-600 hover:text-green-900 bg-green-50 hover:bg-green-100 px-3 py-1 rounded-lg transition-colors duration-200"
                        >
                          Activate
                        </button>
                      )}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📦</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>

      {/* Product Detail Modal */}
      {showProductModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center text-white text-2xl mr-4">
                    {selectedProduct.images[0]}
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                      {selectedProduct.name}
                      {selectedProduct.featured && <span className="ml-2 text-yellow-500">⭐</span>}
                    </h2>
                    <p className="text-gray-600">{selectedProduct.brand} • {selectedProduct.sku}</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowProductModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Product Images */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Images</h3>
                <div className="flex gap-4">
                  {selectedProduct.images.map((image, index) => (
                    <div key={index} className="w-20 h-20 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center text-white text-2xl">
                      {image}
                    </div>
                  ))}
                </div>
              </div>

              {/* Product Info Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Description</label>
                      <p className="text-gray-900">{selectedProduct.description}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Category</label>
                      <p className="text-gray-900">{selectedProduct.category} → {selectedProduct.subcategory}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Brand</label>
                      <p className="text-gray-900">{selectedProduct.brand}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">SKU</label>
                      <p className="text-gray-900 font-mono">{selectedProduct.sku}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Pricing & Inventory</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Current Price</label>
                      <p className="text-gray-900 font-semibold text-lg">₹{selectedProduct.price.toLocaleString()}</p>
                    </div>
                    {selectedProduct.originalPrice > selectedProduct.price && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Original Price</label>
                        <p className="text-gray-500 line-through">₹{selectedProduct.originalPrice.toLocaleString()}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-500">Stock Quantity</label>
                      <p className={`font-semibold ${getStockStatus(selectedProduct).color}`}>
                        {selectedProduct.quantity} units • {getStockStatus(selectedProduct).text}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Low Stock Threshold</label>
                      <p className="text-gray-900">{selectedProduct.lowStockThreshold} units</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Product Attributes */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Attributes</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {Object.entries(selectedProduct.attributes).map(([key, value]) => (
                    <div key={key} className="bg-gray-50 p-3 rounded-lg">
                      <label className="text-sm font-medium text-gray-500 capitalize">{key}</label>
                      <p className="text-gray-900 font-medium">{value}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Performance Metrics */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-blue-600">{selectedProduct.sales}</p>
                    <p className="text-sm text-blue-800">Total Sales</p>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-yellow-600">{selectedProduct.rating}</p>
                    <p className="text-sm text-yellow-800">Average Rating</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-green-600">{selectedProduct.reviews}</p>
                    <p className="text-sm text-green-800">Total Reviews</p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-purple-600">
                      ₹{(selectedProduct.price * selectedProduct.sales).toLocaleString()}
                    </p>
                    <p className="text-sm text-purple-800">Revenue</p>
                  </div>
                </div>
              </div>

              {/* Status and Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Information</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Current Status</label>
                      <div className="mt-1">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedProduct.status)}`}>
                          <span className="mr-1">{getStatusIcon(selectedProduct.status)}</span>
                          {selectedProduct.status.replace('_', ' ').charAt(0).toUpperCase() + selectedProduct.status.replace('_', ' ').slice(1)}
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Featured Product</label>
                      <p className="text-gray-900">{selectedProduct.featured ? '⭐ Yes' : '❌ No'}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Important Dates</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Created Date</label>
                      <p className="text-gray-900">{new Date(selectedProduct.createdDate).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Last Updated</label>
                      <p className="text-gray-900">{new Date(selectedProduct.lastUpdated).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => handleFeaturedToggle(selectedProduct.id)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                    selectedProduct.featured
                      ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {selectedProduct.featured ? 'Remove from Featured' : 'Mark as Featured'}
                </button>

                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200">
                  Edit Product
                </button>

                {selectedProduct.status === 'active' && (
                  <button
                    onClick={() => {
                      handleStatusChange(selectedProduct.id, 'inactive')
                      setShowProductModal(false)
                    }}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors duration-200"
                  >
                    Deactivate Product
                  </button>
                )}

                {selectedProduct.status === 'inactive' && (
                  <button
                    onClick={() => {
                      handleStatusChange(selectedProduct.id, 'active')
                      setShowProductModal(false)
                    }}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200"
                  >
                    Activate Product
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Product Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Add New Product</h2>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6">
              <form className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Product Name</label>
                      <input type="text" className="input-field" placeholder="Enter product name" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                      <input type="text" className="input-field" placeholder="Enter brand name" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                      <select className="input-field">
                        <option value="">Select category</option>
                        <option value="Electronics">Electronics</option>
                        <option value="Fashion">Fashion</option>
                        <option value="Home">Home</option>
                        <option value="Sports">Sports</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">SKU</label>
                      <input type="text" className="input-field" placeholder="Product SKU" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea
                      className="input-field"
                      rows="3"
                      placeholder="Enter product description"
                    ></textarea>
                  </div>
                </div>

                {/* Pricing */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Pricing</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Price (₹)</label>
                      <input type="number" className="input-field" placeholder="0" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Original Price (₹)</label>
                      <input type="number" className="input-field" placeholder="0" />
                    </div>
                  </div>
                </div>

                {/* Inventory */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Inventory</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                      <input type="number" className="input-field" placeholder="0" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Low Stock Threshold</label>
                      <input type="number" className="input-field" placeholder="10" />
                    </div>
                  </div>
                </div>

                {/* Product Images */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Images</h3>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <div className="text-4xl mb-4">📷</div>
                    <p className="text-gray-600 mb-2">Drag and drop images here, or click to select</p>
                    <button type="button" className="btn-secondary">
                      Choose Images
                    </button>
                  </div>
                </div>

                {/* Product Attributes */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Attributes</h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <input type="text" className="input-field" placeholder="Attribute name (e.g., Color)" />
                      <input type="text" className="input-field" placeholder="Attribute value (e.g., Red)" />
                    </div>
                    <button type="button" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                      + Add Another Attribute
                    </button>
                  </div>
                </div>

                {/* Settings */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Settings</h3>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-purple-600 focus:ring-purple-500" />
                      <span className="ml-2 text-gray-700">Mark as featured product</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-purple-600 focus:ring-purple-500" defaultChecked />
                      <span className="ml-2 text-gray-700">Product is active</span>
                    </label>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="btn-secondary flex-1"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary flex-1"
                  >
                    Add Product
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProductManagement
