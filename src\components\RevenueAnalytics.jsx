import { useState } from 'react'
import { useTheme } from '../contexts/ThemeContext'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

function RevenueAnalytics() {
  const { isDarkMode } = useTheme()
  const [timeRange, setTimeRange] = useState('year')
  const [selectedMetric, setSelectedMetric] = useState('revenue')

  // Sample revenue data
  const revenueData = {
    year: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      revenue: [185000, 220000, 195000, 280000, 245000, 320000, 285000, 380000, 295000, 350000, 275000, 425000],
      orders: [1240, 1580, 1320, 1890, 1650, 2100, 1920, 2450, 1980, 2300, 1850, 2800],
      customers: [890, 1120, 980, 1340, 1180, 1520, 1380, 1780, 1420, 1650, 1320, 2010]
    },
    month: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      revenue: [95000, 110000, 85000, 135000],
      orders: [650, 780, 590, 920],
      customers: [420, 510, 380, 600]
    },
    week: {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      revenue: [18000, 22000, 19000, 25000, 28000, 35000, 32000],
      orders: [120, 150, 130, 170, 190, 240, 220],
      customers: [85, 105, 90, 120, 135, 170, 155]
    }
  }

  const currentData = revenueData[timeRange]

  // Chart configuration
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: isDarkMode ? '#e5e7eb' : '#374151',
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
        titleColor: isDarkMode ? '#ffffff' : '#000000',
        bodyColor: isDarkMode ? '#e5e7eb' : '#374151',
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        borderWidth: 1
      }
    },
    scales: {
      x: {
        grid: {
          color: isDarkMode ? '#374151' : '#f3f4f6',
          borderColor: isDarkMode ? '#4b5563' : '#d1d5db'
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280'
        }
      },
      y: {
        grid: {
          color: isDarkMode ? '#374151' : '#f3f4f6',
          borderColor: isDarkMode ? '#4b5563' : '#d1d5db'
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280',
          callback: function(value) {
            return selectedMetric === 'revenue' ? '₹' + value.toLocaleString() : value.toLocaleString()
          }
        }
      }
    }
  }

  const lineChartData = {
    labels: currentData.labels,
    datasets: [
      {
        label: selectedMetric === 'revenue' ? 'Revenue (₹)' : 
               selectedMetric === 'orders' ? 'Orders' : 'Customers',
        data: currentData[selectedMetric],
        borderColor: selectedMetric === 'revenue' ? '#16a085' : 
                    selectedMetric === 'orders' ? '#3b82f6' : '#8b5cf6',
        backgroundColor: selectedMetric === 'revenue' ? 'rgba(22, 160, 133, 0.1)' : 
                        selectedMetric === 'orders' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(139, 92, 246, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: selectedMetric === 'revenue' ? '#16a085' : 
                             selectedMetric === 'orders' ? '#3b82f6' : '#8b5cf6',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      }
    ]
  }

  // Revenue by category data
  const categoryData = {
    labels: ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books', 'Others'],
    datasets: [
      {
        data: [35, 25, 20, 10, 6, 4],
        backgroundColor: [
          '#16a085',
          '#3b82f6',
          '#8b5cf6',
          '#f59e0b',
          '#ef4444',
          '#6b7280'
        ],
        borderWidth: 0,
        hoverOffset: 4
      }
    ]
  }

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: isDarkMode ? '#e5e7eb' : '#374151',
          usePointStyle: true,
          padding: 15
        }
      },
      tooltip: {
        backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
        titleColor: isDarkMode ? '#ffffff' : '#000000',
        bodyColor: isDarkMode ? '#e5e7eb' : '#374151',
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        borderWidth: 1,
        callbacks: {
          label: function(context) {
            return context.label + ': ' + context.parsed + '%'
          }
        }
      }
    }
  }

  // Calculate totals and growth
  const totalRevenue = currentData.revenue.reduce((sum, val) => sum + val, 0)
  const totalOrders = currentData.orders.reduce((sum, val) => sum + val, 0)
  const totalCustomers = currentData.customers.reduce((sum, val) => sum + val, 0)
  const avgOrderValue = totalRevenue / totalOrders

  return (
    <div className={`p-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Revenue Analytics
          </h1>
          <p className={`mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Comprehensive revenue insights and performance metrics
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 mt-4 sm:mt-0">
          <select 
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              isDarkMode 
                ? 'bg-gray-800 text-white border-gray-600 focus:border-teal-500' 
                : 'bg-white text-gray-900 border-gray-300 focus:border-teal-500'
            } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
          >
            <option value="year">This Year</option>
            <option value="month">This Month</option>
            <option value="week">This Week</option>
          </select>
          
          <select 
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              isDarkMode 
                ? 'bg-gray-800 text-white border-gray-600 focus:border-teal-500' 
                : 'bg-white text-gray-900 border-gray-300 focus:border-teal-500'
            } focus:outline-none focus:ring-2 focus:ring-teal-500/20`}
          >
            <option value="revenue">Revenue</option>
            <option value="orders">Orders</option>
            <option value="customers">Customers</option>
          </select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Revenue */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Revenue</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                ₹{totalRevenue.toLocaleString()}
              </p>
              <p className="text-green-500 text-sm font-medium">+12.5% vs last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-teal-400 to-teal-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>

        {/* Total Orders */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Orders</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {totalOrders.toLocaleString()}
              </p>
              <p className="text-blue-500 text-sm font-medium">+8.2% vs last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Total Customers */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Customers</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {totalCustomers.toLocaleString()}
              </p>
              <p className="text-purple-500 text-sm font-medium">+15.3% vs last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Average Order Value */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Avg Order Value</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                ₹{Math.round(avgOrderValue).toLocaleString()}
              </p>
              <p className="text-orange-500 text-sm font-medium">+4.1% vs last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Main Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Revenue Trend Chart */}
        <div className="lg:col-span-2">
          <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)} Trend
              </h2>
              <div className="flex items-center space-x-2">
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  isDarkMode ? 'bg-teal-900 text-teal-300' : 'bg-teal-100 text-teal-800'
                }`}>
                  {timeRange.charAt(0).toUpperCase() + timeRange.slice(1)}
                </div>
              </div>
            </div>
            <div className="h-80">
              <Line data={lineChartData} options={chartOptions} />
            </div>
          </div>
        </div>

        {/* Revenue by Category */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <h3 className={`text-lg font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Revenue by Category
          </h3>
          <div className="h-80">
            <Doughnut data={categoryData} options={doughnutOptions} />
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Top Performing Products */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <h3 className={`text-lg font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Top Performing Products
          </h3>
          <div className="space-y-4">
            {[
              { name: 'iPhone 15 Pro', revenue: 125000, growth: 15.2 },
              { name: 'Samsung Galaxy S24', revenue: 98000, growth: 12.8 },
              { name: 'MacBook Air M3', revenue: 87000, growth: 8.5 },
              { name: 'Sony WH-1000XM5', revenue: 65000, growth: 22.1 },
              { name: 'iPad Pro 12.9"', revenue: 54000, growth: 6.3 }
            ].map((product, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' : 'bg-teal-500'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {product.name}
                    </p>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      ₹{product.revenue.toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-sm font-medium ${product.growth > 10 ? 'text-green-500' : 'text-blue-500'}`}>
                    +{product.growth}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Revenue Goals */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <h3 className={`text-lg font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Revenue Goals
          </h3>
          <div className="space-y-6">
            {/* Monthly Goal */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Monthly Goal
                </span>
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  ₹4,25,000 / ₹5,00,000
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div className="bg-gradient-to-r from-teal-400 to-teal-600 h-3 rounded-full" style={{width: '85%'}}></div>
              </div>
              <p className="text-xs text-teal-500 mt-1">85% completed</p>
            </div>

            {/* Quarterly Goal */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Quarterly Goal
                </span>
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  ₹11,50,000 / ₹15,00,000
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full" style={{width: '77%'}}></div>
              </div>
              <p className="text-xs text-blue-500 mt-1">77% completed</p>
            </div>

            {/* Annual Goal */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Annual Goal
                </span>
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  ₹32,75,000 / ₹50,00,000
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div className="bg-gradient-to-r from-purple-400 to-purple-600 h-3 rounded-full" style={{width: '66%'}}></div>
              </div>
              <p className="text-xs text-purple-500 mt-1">66% completed</p>
            </div>

            {/* Goal Achievement Insights */}
            <div className={`mt-6 p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <h4 className={`text-sm font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Goal Insights
              </h4>
              <ul className={`text-xs space-y-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <li>• On track to exceed monthly goal</li>
                <li>• Need 23% more for quarterly target</li>
                <li>• Trending well for annual goal</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Breakdown Table */}
      <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
        <div className="flex justify-between items-center mb-6">
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Revenue Breakdown
          </h3>
          <button className={`px-4 py-2 text-sm font-medium rounded-lg ${
            isDarkMode
              ? 'bg-teal-600 hover:bg-teal-700 text-white'
              : 'bg-teal-600 hover:bg-teal-700 text-white'
          } transition-colors`}>
            Export Report
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <th className={`text-left py-3 px-4 font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Period
                </th>
                <th className={`text-left py-3 px-4 font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Revenue
                </th>
                <th className={`text-left py-3 px-4 font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Orders
                </th>
                <th className={`text-left py-3 px-4 font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Avg Order Value
                </th>
                <th className={`text-left py-3 px-4 font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Growth
                </th>
              </tr>
            </thead>
            <tbody>
              {currentData.labels.map((label, index) => {
                const revenue = currentData.revenue[index]
                const orders = currentData.orders[index]
                const aov = Math.round(revenue / orders)
                const growth = index > 0 ? ((revenue - currentData.revenue[index - 1]) / currentData.revenue[index - 1] * 100) : 0

                return (
                  <tr key={index} className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <td className={`py-3 px-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {label}
                    </td>
                    <td className={`py-3 px-4 font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      ₹{revenue.toLocaleString()}
                    </td>
                    <td className={`py-3 px-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {orders.toLocaleString()}
                    </td>
                    <td className={`py-3 px-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      ₹{aov.toLocaleString()}
                    </td>
                    <td className={`py-3 px-4`}>
                      <span className={`text-sm font-medium ${
                        growth > 0 ? 'text-green-500' : growth < 0 ? 'text-red-500' : 'text-gray-500'
                      }`}>
                        {growth > 0 ? '+' : ''}{growth.toFixed(1)}%
                      </span>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default RevenueAnalytics
