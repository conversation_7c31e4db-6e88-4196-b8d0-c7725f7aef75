import React, { useState, useEffect, useRef } from 'react'
import { useTheme } from '../contexts/ThemeContext'

function Categories() {
  const { isDarkMode } = useTheme()
  const [filters, setFilters] = useState({
    fromDate: '',
    toDate: '',
    searchQuery: '',
    parentCategory: 'All Categories'
  })
  const [selectedCategories, setSelectedCategories] = useState([])
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false)
  const [currentView, setCurrentView] = useState('manage') // 'manage' or 'add'
  const dropdownRef = useRef(null)

  // Sample categories data
  const allCategories = [
    {
      id: 1,
      image: '🥬',
      name: 'Fresh Vegetables',
      status: 'Active',
      dateAdded: '2025-07-01',
      parentCategory: null,
      description: 'Fresh and organic vegetables'
    },
    {
      id: 2,
      image: '🍎',
      name: 'Fresh Fruits',
      status: 'Active',
      dateAdded: '2025-07-01',
      parentCategory: null,
      description: 'Seasonal fresh fruits'
    },
    {
      id: 3,
      image: '🥛',
      name: 'Dairy Products',
      status: 'Active',
      dateAdded: '2025-07-02',
      parentCategory: null,
      description: 'Milk, cheese, yogurt and more'
    },
    {
      id: 4,
      image: '🍞',
      name: 'Bakery Items',
      status: 'Active',
      dateAdded: '2025-07-02',
      parentCategory: null,
      description: 'Fresh bread and baked goods'
    },
    {
      id: 5,
      image: '🥤',
      name: 'Beverages',
      status: 'Active',
      dateAdded: '2025-07-03',
      parentCategory: null,
      description: 'Soft drinks, juices and water'
    },
    {
      id: 6,
      image: '🍿',
      name: 'Snacks',
      status: 'Active',
      dateAdded: '2025-07-03',
      parentCategory: null,
      description: 'Chips, crackers and snacks'
    },
    {
      id: 7,
      image: '🍖',
      name: 'Meat & Seafood',
      status: 'Active',
      dateAdded: '2025-07-04',
      parentCategory: null,
      description: 'Fresh meat and seafood'
    },
    {
      id: 8,
      image: '🧴',
      name: 'Personal Care',
      status: 'Deactivated',
      dateAdded: '2025-07-04',
      parentCategory: null,
      description: 'Health and beauty products'
    },
    {
      id: 9,
      image: '🧽',
      name: 'Household Items',
      status: 'Active',
      dateAdded: '2025-07-05',
      parentCategory: null,
      description: 'Cleaning and household supplies'
    },
    {
      id: 10,
      image: '🍝',
      name: 'Pantry Staples',
      status: 'Active',
      dateAdded: '2025-07-05',
      parentCategory: null,
      description: 'Rice, pasta, oils and spices'
    }
  ]

  // Filter categories based on criteria
  const filteredCategories = allCategories.filter(category => {
    const categoryDate = new Date(category.dateAdded)
    const fromDate = new Date(filters.fromDate)
    const toDate = new Date(filters.toDate)
    
    const matchesDateRange = (!filters.fromDate || categoryDate >= fromDate) && 
                            (!filters.toDate || categoryDate <= toDate)
    const matchesSearch = !filters.searchQuery || 
                         category.name.toLowerCase().includes(filters.searchQuery.toLowerCase())
    const matchesParent = filters.parentCategory === 'All Categories' || 
                         category.parentCategory === filters.parentCategory
    
    return matchesDateRange && matchesSearch && matchesParent
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredCategories.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedCategories = filteredCategories.slice(startIndex, startIndex + itemsPerPage)

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  // Reset filters
  const resetFilters = () => {
    setFilters({
      fromDate: '',
      toDate: '',
      searchQuery: '',
      parentCategory: 'All Categories'
    })
    setCurrentPage(1)
  }

  // Handle status toggle
  const handleStatusToggle = (categoryId) => {
    // In real app, this would make an API call
    console.log('Toggle status for category:', categoryId)
  }

  // Handle select all
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedCategories(paginatedCategories.map(category => category.id))
    } else {
      setSelectedCategories([])
    }
  }

  // Handle individual category selection
  const handleCategorySelect = (categoryId, checked) => {
    if (checked) {
      setSelectedCategories(prev => [...prev, categoryId])
    } else {
      setSelectedCategories(prev => prev.filter(id => id !== categoryId))
    }
  }

  const getStatusColor = (status) => {
    return status === 'Active'
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-red-100 text-red-800 border-red-200'
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowCategoryDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="p-6">
        {/* Page Title & Breadcrumb */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <nav className={`text-sm mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              <span>Dashboard</span>
              <span className="mx-2">/</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Categories</span>
              {currentView === 'add' && (
                <>
                  <span className="mx-2">/</span>
                  <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Add Category</span>
                </>
              )}
            </nav>
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {currentView === 'add' ? 'Add Category' : 'Manage Categories'}
            </h1>
          </div>

          {/* Category Actions Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
              className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 flex items-center space-x-2"
            >
              <span>📂</span>
              <span>Category Actions</span>
              <span className={`transform transition-transform duration-200 ${showCategoryDropdown ? 'rotate-180' : ''}`}>
                ▼
              </span>
            </button>

            {/* Dropdown Menu */}
            {showCategoryDropdown && (
              <div className={`absolute right-0 mt-2 w-56 rounded-lg shadow-lg z-50 ${
                isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
              }`}>
                <div className="py-2">
                  <button
                    onClick={() => {
                      setCurrentView('add')
                      setShowCategoryDropdown(false)
                    }}
                    className={`w-full text-left px-4 py-2 text-sm flex items-center space-x-3 hover:${
                      isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                    } transition-colors duration-200 ${
                      currentView === 'add' ? (isDarkMode ? 'bg-gray-700 text-green-400' : 'bg-green-50 text-green-600') : ''
                    }`}
                  >
                    <span>➕</span>
                    <div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Add Category</div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Create new category</div>
                    </div>
                  </button>

                  <button
                    onClick={() => {
                      setCurrentView('manage')
                      setShowCategoryDropdown(false)
                    }}
                    className={`w-full text-left px-4 py-2 text-sm flex items-center space-x-3 hover:${
                      isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                    } transition-colors duration-200 ${
                      currentView === 'manage' ? (isDarkMode ? 'bg-gray-700 text-green-400' : 'bg-green-50 text-green-600') : ''
                    }`}
                  >
                    <span>🗂️</span>
                    <div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Manage Categories</div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>View and edit categories</div>
                    </div>
                  </button>

                  <hr className={`my-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`} />

                  <button
                    onClick={() => {
                      setShowCategoryDropdown(false)
                      // Add bulk import functionality
                    }}
                    className={`w-full text-left px-4 py-2 text-sm flex items-center space-x-3 hover:${
                      isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                    } transition-colors duration-200`}
                  >
                    <span>📤</span>
                    <div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Import Categories</div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Bulk import from CSV</div>
                    </div>
                  </button>

                  <button
                    onClick={() => {
                      setShowCategoryDropdown(false)
                      // Add export functionality
                    }}
                    className={`w-full text-left px-4 py-2 text-sm flex items-center space-x-3 hover:${
                      isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                    } transition-colors duration-200`}
                  >
                    <span>📥</span>
                    <div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Export Categories</div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Download as CSV/PDF</div>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Filters Section */}
        <div className={`rounded-xl p-6 mb-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* From Date */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>From Date</label>
              <input
                type="date"
                value={filters.fromDate}
                onChange={(e) => handleFilterChange('fromDate', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* To Date */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>To Date</label>
              <input
                type="date"
                value={filters.toDate}
                onChange={(e) => handleFilterChange('toDate', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* Parent Category */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Parent Category</label>
              <select
                value={filters.parentCategory}
                onChange={(e) => handleFilterChange('parentCategory', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="All Categories">All Categories</option>
                <option value="Main Category">Main Category</option>
                <option value="Sub Category">Sub Category</option>
              </select>
            </div>

            {/* Search */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Search</label>
              <input
                type="text"
                placeholder="Search by category name..."
                value={filters.searchQuery}
                onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={resetFilters}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-all duration-200 flex items-center space-x-2"
            >
              <span>🗑️</span>
              <span>Clear</span>
            </button>
            
            <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 flex items-center space-x-2">
              <span>🔄</span>
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Add Category Form */}
        {currentView === 'add' && (
          <div className={`rounded-xl p-6 mb-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <h2 className={`text-xl font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Create New Category
            </h2>

            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Category Name */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Category Name *
                  </label>
                  <input
                    type="text"
                    placeholder="Enter category name"
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                      isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                </div>

                {/* Parent Category */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Parent Category
                  </label>
                  <select className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                  }`}>
                    <option value="">Select Parent Category (Optional)</option>
                    <option value="1">Fresh Vegetables</option>
                    <option value="2">Fresh Fruits</option>
                    <option value="3">Dairy Products</option>
                    <option value="4">Bakery Items</option>
                  </select>
                </div>

                {/* Category Image */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Category Image
                  </label>
                  <div className={`border-2 border-dashed rounded-lg p-6 text-center ${
                    isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'
                  }`}>
                    <div className="text-4xl mb-2">📁</div>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Click to upload or drag and drop
                    </p>
                    <p className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                      PNG, JPG up to 2MB
                    </p>
                    <input type="file" className="hidden" accept="image/*" />
                  </div>
                </div>

                {/* Status */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Status
                  </label>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center cursor-pointer">
                      <input type="radio" name="status" value="active" defaultChecked className="sr-only" />
                      <div className="w-4 h-4 border-2 border-green-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      </div>
                      <span className={`ml-2 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Active</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input type="radio" name="status" value="inactive" className="sr-only" />
                      <div className="w-4 h-4 border-2 border-gray-400 rounded-full"></div>
                      <span className={`ml-2 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Inactive</span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Description
                </label>
                <textarea
                  rows="4"
                  placeholder="Enter category description"
                  className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                ></textarea>
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => setCurrentView('manage')}
                  className={`px-6 py-2 rounded-lg border transition-all duration-200 ${
                    isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200"
                >
                  Create Category
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Bulk Actions - Only show in manage view */}
        {currentView === 'manage' && selectedCategories.length > 0 && (
          <div className={`rounded-xl p-4 mb-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <div className="flex items-center justify-between">
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {selectedCategories.length} category(ies) selected
              </span>
              <div className="flex gap-2">
                <select className={`px-3 py-1 rounded border text-sm ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}>
                  <option>Bulk Actions</option>
                  <option>Activate Selected</option>
                  <option>Deactivate Selected</option>
                  <option>Delete Selected</option>
                </select>
                <button className="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded text-sm">
                  Apply
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Categories Table - Only show in manage view */}
        {currentView === 'manage' && (
          <div className={`rounded-xl ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm overflow-hidden`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Categories ({filteredCategories.length} total)
              </h2>
              <div className="flex gap-2">
                <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 text-sm">
                  📊 Export CSV
                </button>
                <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 text-sm">
                  📄 Export PDF
                </button>
              </div>
            </div>
          </div>

          {paginatedCategories.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📂</span>
              </div>
              <h3 className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>No categories found</h3>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                No categories match your current filters. Try adjusting your search criteria.
              </p>
            </div>
          ) : (
            <>
              {/* Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <tr>
                      <th className="px-4 py-3 text-left">
                        <input
                          type="checkbox"
                          checked={selectedCategories.length === paginatedCategories.length && paginatedCategories.length > 0}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        ID
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Image
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Category Name
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Status
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Date Added
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                    {paginatedCategories.map((category) => (
                      <tr key={category.id} className={`hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                        <td className="px-4 py-4">
                          <input
                            type="checkbox"
                            checked={selectedCategories.includes(category.id)}
                            onChange={(e) => handleCategorySelect(category.id, e.target.checked)}
                            className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                          />
                        </td>
                        <td className={`px-4 py-4 text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {category.id}
                        </td>
                        <td className="px-4 py-4">
                          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-2xl">
                            {category.image}
                          </div>
                        </td>
                        <td className={`px-4 py-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          <div>
                            <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                              {category.name}
                            </div>
                            <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {category.description}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4">
                          <label className="flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={category.status === 'Active'}
                              onChange={() => handleStatusToggle(category.id)}
                              className="sr-only"
                            />
                            <div className={`relative w-11 h-6 rounded-full transition-colors duration-200 ${
                              category.status === 'Active' ? 'bg-green-500' : 'bg-gray-300'
                            }`}>
                              <div className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200 ${
                                category.status === 'Active' ? 'translate-x-5' : 'translate-x-0'
                              }`}></div>
                            </div>
                            <span className={`ml-2 text-sm ${
                              category.status === 'Active' ? 'text-green-600' : 'text-gray-500'
                            }`}>
                              {category.status}
                            </span>
                          </label>
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {new Date(category.dateAdded).toLocaleDateString('en-IN')}
                        </td>
                        <td className="px-4 py-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => {
                                setSelectedCategory(category)
                                setShowEditModal(true)
                              }}
                              className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors duration-200"
                              title="Edit Category"
                            >
                              ✏️
                            </button>
                            <button
                              onClick={() => {
                                setSelectedCategory(category)
                                setShowDeleteModal(true)
                              }}
                              className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors duration-200"
                              title="Delete Category"
                            >
                              🗑️
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Per page:</span>
                    <select
                      value={itemsPerPage}
                      onChange={(e) => {
                        setItemsPerPage(Number(e.target.value))
                        setCurrentPage(1)
                      }}
                      className={`px-2 py-1 rounded border text-sm ${
                        isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value={5}>5</option>
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                    </select>
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredCategories.length)} of {filteredCategories.length} categories
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === 1
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      «
                    </button>
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === 1
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      ‹
                    </button>

                    {/* Page Numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else {
                        const start = Math.max(1, currentPage - 2);
                        const end = Math.min(totalPages, start + 4);
                        const adjustedStart = Math.max(1, end - 4);
                        pageNum = adjustedStart + i;
                      }

                      if (pageNum > totalPages) return null;

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`px-3 py-1 rounded text-sm ${
                            currentPage === pageNum
                              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
                              : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      )
                    }).filter(Boolean)}

                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === totalPages
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      ›
                    </button>
                    <button
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === totalPages
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      »
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
        )}
      </div>
    </div>
  )
}

export default Categories
