import React, { useState, useEffect, useRef } from 'react'
import { useTheme } from '../contexts/ThemeContext'
import { Link } from 'react-router-dom'

function ProductList() {
  const { isDarkMode } = useTheme()
  const [filters, setFilters] = useState({
    category: 'All Categories',
    status: 'All Status',
    dateAdded: '',
    searchQuery: ''
  })
  const [selectedProducts, setSelectedProducts] = useState([])
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [showBulkActions, setShowBulkActions] = useState(false)
  const bulkActionsRef = useRef(null)

  // Sample products data
  const allProducts = [
    {
      id: 1,
      productId: 'PRD001',
      name: 'Fresh Organic Tomatoes',
      seller: 'Green Valley Farms',
      category: 'Fresh Vegetables',
      subCategory: 'Tomatoes',
      image: '🍅',
      mrp: 80,
      sellingPrice: 65,
      stock: 150,
      discount: 18.75,
      status: 'Active',
      dateAdded: '2025-07-01'
    },
    {
      id: 2,
      productId: 'PRD002',
      name: 'Premium Basmati Rice 1kg',
      seller: 'Rice Masters',
      category: 'Pantry Staples',
      subCategory: 'Rice',
      image: '🍚',
      mrp: 120,
      sellingPrice: 95,
      stock: 200,
      discount: 20.83,
      status: 'Active',
      dateAdded: '2025-07-02'
    },
    {
      id: 3,
      productId: 'PRD003',
      name: 'Fresh Milk 1L',
      seller: 'Dairy Fresh Co.',
      category: 'Dairy Products',
      subCategory: 'Milk',
      image: '🥛',
      mrp: 60,
      sellingPrice: 55,
      stock: 80,
      discount: 8.33,
      status: 'Active',
      dateAdded: '2025-07-03'
    },
    {
      id: 4,
      productId: 'PRD004',
      name: 'Whole Wheat Bread',
      seller: 'Baker\'s Delight',
      category: 'Bakery Items',
      subCategory: 'Bread',
      image: '🍞',
      mrp: 45,
      sellingPrice: 40,
      stock: 0,
      discount: 11.11,
      status: 'Inactive',
      dateAdded: '2025-07-04'
    },
    {
      id: 5,
      productId: 'PRD005',
      name: 'Fresh Orange Juice 500ml',
      seller: 'Citrus Fresh',
      category: 'Beverages',
      subCategory: 'Juices',
      image: '🧃',
      mrp: 85,
      sellingPrice: 75,
      stock: 120,
      discount: 11.76,
      status: 'Active',
      dateAdded: '2025-07-05'
    },
    {
      id: 6,
      productId: 'PRD006',
      name: 'Potato Chips 100g',
      seller: 'Snack World',
      category: 'Snacks',
      subCategory: 'Chips',
      image: '🍿',
      mrp: 30,
      sellingPrice: 25,
      stock: 300,
      discount: 16.67,
      status: 'Active',
      dateAdded: '2025-07-06'
    },
    {
      id: 7,
      productId: 'PRD007',
      name: 'Fresh Chicken 1kg',
      seller: 'Meat Masters',
      category: 'Meat & Seafood',
      subCategory: 'Chicken',
      image: '🍖',
      mrp: 280,
      sellingPrice: 250,
      stock: 50,
      discount: 10.71,
      status: 'Active',
      dateAdded: '2025-07-07'
    },
    {
      id: 8,
      productId: 'PRD008',
      name: 'Shampoo 200ml',
      seller: 'Beauty Care',
      category: 'Personal Care',
      subCategory: 'Hair Care',
      image: '🧴',
      mrp: 150,
      sellingPrice: 120,
      stock: 75,
      discount: 20,
      status: 'Active',
      dateAdded: '2025-07-08'
    }
  ]

  // Filter products based on criteria
  const filteredProducts = allProducts.filter(product => {
    const productDate = new Date(product.dateAdded)
    const filterDate = new Date(filters.dateAdded)
    
    const matchesCategory = filters.category === 'All Categories' || product.category === filters.category
    const matchesStatus = filters.status === 'All Status' || product.status === filters.status
    const matchesDate = !filters.dateAdded || productDate >= filterDate
    const matchesSearch = !filters.searchQuery || 
                         product.name.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
                         product.productId.toLowerCase().includes(filters.searchQuery.toLowerCase())
    
    return matchesCategory && matchesStatus && matchesDate && matchesSearch
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage)

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  // Reset filters
  const resetFilters = () => {
    setFilters({
      category: 'All Categories',
      status: 'All Status',
      dateAdded: '',
      searchQuery: ''
    })
    setCurrentPage(1)
  }

  // Handle status toggle
  const handleStatusToggle = (productId) => {
    console.log('Toggle status for product:', productId)
  }

  // Handle select all
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedProducts(paginatedProducts.map(product => product.id))
    } else {
      setSelectedProducts([])
    }
  }

  // Handle individual product selection
  const handleProductSelect = (productId, checked) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId])
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId))
    }
  }

  // Close bulk actions dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (bulkActionsRef.current && !bulkActionsRef.current.contains(event.target)) {
        setShowBulkActions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const getStockStatus = (stock) => {
    if (stock === 0) return { text: 'Out of Stock', color: 'text-red-600 bg-red-100' }
    if (stock < 50) return { text: 'Low Stock', color: 'text-yellow-600 bg-yellow-100' }
    return { text: 'In Stock', color: 'text-green-600 bg-green-100' }
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="p-6">
        {/* Page Title & Breadcrumb */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <nav className={`text-sm mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              <span>Dashboard</span>
              <span className="mx-2">/</span>
              <span>Products</span>
              <span className="mx-2">/</span>
              <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Product List</span>
            </nav>
            <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Product List</h1>
          </div>
          
          <div className="flex gap-2">
            <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 flex items-center space-x-2">
              <span>📊</span>
              <span>Export CSV</span>
            </button>
            <Link
              to="/products/create"
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 flex items-center space-x-2"
            >
              <span>➕</span>
              <span>Add Product</span>
            </Link>
          </div>
        </div>

        {/* Filters Section */}
        <div className={`rounded-xl p-6 mb-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* Category Filter */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Category</label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="All Categories">All Categories</option>
                <option value="Fresh Vegetables">Fresh Vegetables</option>
                <option value="Fresh Fruits">Fresh Fruits</option>
                <option value="Dairy Products">Dairy Products</option>
                <option value="Bakery Items">Bakery Items</option>
                <option value="Beverages">Beverages</option>
                <option value="Snacks">Snacks</option>
                <option value="Meat & Seafood">Meat & Seafood</option>
                <option value="Personal Care">Personal Care</option>
                <option value="Pantry Staples">Pantry Staples</option>
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="All Status">All Status</option>
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
              </select>
            </div>

            {/* Date Filter */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Date Added</label>
              <input
                type="date"
                value={filters.dateAdded}
                onChange={(e) => handleFilterChange('dateAdded', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* Search */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Search</label>
              <input
                type="text"
                placeholder="Search by name or ID..."
                value={filters.searchQuery}
                onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={resetFilters}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-all duration-200 flex items-center space-x-2"
            >
              <span>🗑️</span>
              <span>Clear</span>
            </button>

            <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 flex items-center space-x-2">
              <span>🔄</span>
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedProducts.length > 0 && (
          <div className={`rounded-xl p-4 mb-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <div className="flex items-center justify-between">
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {selectedProducts.length} product(s) selected
              </span>
              <div className="relative" ref={bulkActionsRef}>
                <button
                  onClick={() => setShowBulkActions(!showBulkActions)}
                  className="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded text-sm flex items-center space-x-1"
                >
                  <span>Actions</span>
                  <span className={`transform transition-transform duration-200 ${showBulkActions ? 'rotate-180' : ''}`}>▼</span>
                </button>

                {showBulkActions && (
                  <div className={`absolute mt-2 right-0 w-48 rounded-lg shadow-lg z-50 ${
                    isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
                  }`}>
                    <div className="py-2">
                      <button className={`w-full text-left px-4 py-2 text-sm hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                        📊 Export Selected
                      </button>
                      <button className={`w-full text-left px-4 py-2 text-sm hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                        ✅ Activate Selected
                      </button>
                      <button className={`w-full text-left px-4 py-2 text-sm hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                        ❌ Deactivate Selected
                      </button>
                      <hr className={`my-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`} />
                      <button className={`w-full text-left px-4 py-2 text-sm text-red-600 hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                        🗑️ Delete Selected
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Products Table */}
        <div className={`rounded-xl ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm overflow-hidden`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Products ({filteredProducts.length} total)
              </h2>
            </div>
          </div>

          {paginatedProducts.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📦</span>
              </div>
              <h3 className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>No products found</h3>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                No products match your current filters. Try adjusting your search criteria.
              </p>
            </div>
          ) : (
            <>
              {/* Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <tr>
                      <th className="px-4 py-3 text-left">
                        <input
                          type="checkbox"
                          checked={selectedProducts.length === paginatedProducts.length && paginatedProducts.length > 0}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        ID
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Product ID
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Product Name
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Seller
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Category
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Image
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Price
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Stock
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Discount
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Status
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                    {paginatedProducts.map((product) => {
                      const stockStatus = getStockStatus(product.stock)
                      return (
                        <tr key={product.id} className={`hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                          <td className="px-4 py-4">
                            <input
                              type="checkbox"
                              checked={selectedProducts.includes(product.id)}
                              onChange={(e) => handleProductSelect(product.id, e.target.checked)}
                              className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                            />
                          </td>
                          <td className={`px-4 py-4 text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {product.id}
                          </td>
                          <td className={`px-4 py-4 text-sm font-medium ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                            {product.productId}
                          </td>
                          <td className={`px-4 py-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center text-lg mr-3">
                                {product.image}
                              </div>
                              <div>
                                <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                  {product.name}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            {product.seller}
                          </td>
                          <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <div>
                              <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {product.category}
                              </div>
                              <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                {product.subCategory}
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-2xl">
                              {product.image}
                            </div>
                          </td>
                          <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <div>
                              <div className={`font-medium text-green-600`}>
                                ₹{product.sellingPrice}
                              </div>
                              <div className={`text-xs line-through ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                ₹{product.mrp}
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div>
                              <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {product.stock}
                              </div>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${stockStatus.color}`}>
                                {stockStatus.text}
                              </span>
                            </div>
                          </td>
                          <td className={`px-4 py-4 text-sm font-medium text-orange-600`}>
                            {product.discount.toFixed(1)}%
                          </td>
                          <td className="px-4 py-4">
                            <label className="flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={product.status === 'Active'}
                                onChange={() => handleStatusToggle(product.id)}
                                className="sr-only"
                              />
                              <div className={`relative w-11 h-6 rounded-full transition-colors duration-200 ${
                                product.status === 'Active' ? 'bg-green-500' : 'bg-gray-300'
                              }`}>
                                <div className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200 ${
                                  product.status === 'Active' ? 'translate-x-5' : 'translate-x-0'
                                }`}></div>
                              </div>
                            </label>
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex space-x-2">
                              <button
                                className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors duration-200"
                                title="View Product"
                              >
                                👁️
                              </button>
                              <Link
                                to={`/products/edit/${product.id}`}
                                className="p-1 text-green-600 hover:bg-green-100 rounded transition-colors duration-200"
                                title="Edit Product"
                              >
                                🖊️
                              </Link>
                              <button
                                className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors duration-200"
                                title="Delete Product"
                              >
                                🗑️
                              </button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Per page:</span>
                    <select
                      value={itemsPerPage}
                      onChange={(e) => {
                        setItemsPerPage(Number(e.target.value))
                        setCurrentPage(1)
                      }}
                      className={`px-2 py-1 rounded border text-sm ${
                        isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value={5}>5</option>
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                    </select>
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredProducts.length)} of {filteredProducts.length} products
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === 1
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      «
                    </button>
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === 1
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      ‹
                    </button>

                    {/* Page Numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else {
                        const start = Math.max(1, currentPage - 2);
                        const end = Math.min(totalPages, start + 4);
                        const adjustedStart = Math.max(1, end - 4);
                        pageNum = adjustedStart + i;
                      }

                      if (pageNum > totalPages) return null;

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`px-3 py-1 rounded text-sm ${
                            currentPage === pageNum
                              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
                              : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      )
                    }).filter(Boolean)}

                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === totalPages
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      ›
                    </button>
                    <button
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === totalPages
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      »
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default ProductList
