/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "14", height: "6", x: "5", y: "12", rx: "2", key: "4l4tp2" }],
  ["rect", { width: "10", height: "6", x: "7", y: "2", rx: "2", key: "ypihtt" }],
  ["path", { d: "M2 22h20", key: "272qi7" }]
];
const AlignVerticalJustifyEnd = createLucideIcon("align-vertical-justify-end", __iconNode);

export { __iconNode, AlignVerticalJustifyEnd as default };
//# sourceMappingURL=align-vertical-justify-end.js.map
