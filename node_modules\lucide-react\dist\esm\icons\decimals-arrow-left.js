/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m13 21-3-3 3-3", key: "s3o1nf" }],
  ["path", { d: "M20 18H10", key: "14r3mt" }],
  ["path", { d: "M3 11h.01", key: "1eifu7" }],
  ["rect", { x: "6", y: "3", width: "5", height: "8", rx: "2.5", key: "v9paqo" }]
];
const DecimalsArrowLeft = createLucideIcon("decimals-arrow-left", __iconNode);

export { __iconNode, DecimalsArrowLeft as default };
//# sourceMappingURL=decimals-arrow-left.js.map
