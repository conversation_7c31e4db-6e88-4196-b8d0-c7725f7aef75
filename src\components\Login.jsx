import { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useTheme } from '../contexts/ThemeContext'
import { Navigate } from 'react-router-dom'

function Login() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const { login, isAuthenticated } = useAuth()
  const { isDarkMode } = useTheme()

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" />
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    const result = await login(email, password)

    if (!result.success) {
      setError(result.error)
    }

    setLoading(false)
  }

  const handleSellerPanel = () => {
    alert('Seller Panel - Coming Soon!')
  }

  const handleDeliveryPanel = () => {
    alert('Delivery Boy Panel - Coming Soon!')
  }

  return (
    <div className="min-h-screen flex">
      {/* Full Background - Shopping Cart with Vegetables */}
      <div className="absolute inset-0">
        {/* Background Image - Shopping-cart 2.png natural colors */}
        <div
          className="w-full h-full bg-no-repeat"
          style={{
            backgroundImage: `url('/Shopping-cart-2.png?v=${Date.now()}')`,
            filter: 'contrast(1.1) saturate(1.2)',
            backgroundSize: 'contain',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
            backgroundColor: '#f8fafc'
          }}
        >
          {/* Fallback image if CSS background fails */}
          <img
            src={`/Shopping-cart-2.png?v=${Date.now()}`}
            alt="Shopping Cart with Fresh Vegetables"
            className="w-full h-full object-contain opacity-0"
            style={{ objectPosition: 'center center' }}
            onError={(e) => {
              console.log('Shopping-cart-2.png failed, trying backup...')
              // Try the original shopping cart image
              e.target.src = '/Shopping-cart.png'
              e.target.style.opacity = '1'
              e.target.parentElement.style.backgroundImage = `url('/Shopping-cart.png')`
              e.target.onerror = () => {
                // If both fail, show fallback
                e.target.style.display = 'none'
                e.target.nextSibling.style.display = 'block'
              }
            }}
          />
          {/* Fallback gradient background */}
          <div className="hidden w-full h-full bg-gradient-to-br from-green-400 via-green-500 to-green-600 flex items-center justify-center">
            <div className="text-center text-white">
              <div className="text-6xl mb-4">🛒</div>
              <p className="text-lg">Shopping Cart Background</p>
            </div>
          </div>
        </div>

        {/* Dark overlay for better contrast */}
        <div className="absolute inset-0 bg-black/20"></div>
      </div>

      {/* Login Form - Moved more to the right with increased width */}
      <div className="relative z-10 w-full flex items-center justify-end" style={{ paddingRight: '15%' }}>
        <div className="w-full max-w-md">
          {/* Modern Login Card - Dynamic theme */}
          <div className={`backdrop-blur-xl rounded-2xl p-10 shadow-2xl border transition-all duration-300 ${
            isDarkMode
              ? 'bg-gray-800/95 border-gray-700/50'
              : 'bg-white/95 border-white/30'
          }`}>
            {/* Logo and Brand - Dynamic theme styling */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-6">
                {/* Logo Container - Dynamic background */}
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center shadow-lg p-0.5 ${
                  isDarkMode ? 'bg-white' : 'bg-gray-100'
                }`}>
                  <img
                    src="/elitemart-logo.png"
                    alt="EliteMart Logo"
                    className="w-11 h-11 object-contain"
                    onError={(e) => {
                      e.target.style.display = 'none'
                      e.target.nextSibling.style.display = 'flex'
                    }}
                  />
                  {/* Fallback - E letter */}
                  <div className="hidden w-full h-full flex items-center justify-center">
                    <span className="text-green-600 font-bold text-2xl">E</span>
                  </div>
                </div>
                <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  EliteMart
                </h1>
              </div>

              <h2 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Welcome Back!
              </h2>
              <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Please login to your Account
              </p>
            </div>

            {error && (
              <div className={`border px-4 py-3 rounded-lg mb-6 backdrop-blur-sm ${
                isDarkMode
                  ? 'bg-red-500/20 border-red-500/30 text-red-300'
                  : 'bg-red-50 border-red-200 text-red-700'
              }`}>
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field - Dynamic theme styling */}
              <div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg className={`h-5 w-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className={`w-full pl-12 pr-4 py-4 border rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 backdrop-blur-sm ${
                      isDarkMode
                        ? 'bg-gray-700/80 border-gray-600/50 text-white placeholder-gray-400'
                        : 'bg-white/80 border-gray-300/50 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              {/* Password Field - Dynamic theme styling */}
              <div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg className={`h-5 w-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={`w-full pl-12 pr-14 py-4 border rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 backdrop-blur-sm ${
                      isDarkMode
                        ? 'bg-gray-700/80 border-gray-600/50 text-white placeholder-gray-400'
                        : 'bg-white/80 border-gray-300/50 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="••••••"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className={`absolute inset-y-0 right-0 pr-4 flex items-center transition-colors ${
                      isDarkMode
                        ? 'text-gray-400 hover:text-white'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {showPassword ? (
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              {/* Forgot Password */}
              <div className="text-right">
                <a href="#" className={`text-sm hover:text-green-400 transition-colors ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  Forgot Password?
                </a>
              </div>

              {/* Login Button - Same styling for both modes */}
              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </div>
                ) : (
                  <>
                    Login
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </>
                )}
              </button>
            </form>

            {/* Additional Buttons - Same green styling as Login */}
            <div className="space-y-3 mt-6">
              <button
                onClick={handleSellerPanel}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Seller Panel
              </button>

              <button
                onClick={handleDeliveryPanel}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Delivery Boy Panel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
