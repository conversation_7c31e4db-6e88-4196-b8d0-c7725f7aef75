import React, { useState } from 'react'
import { useTheme } from '../contexts/ThemeContext'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import { Line, Bar } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

function Dashboard() {
  const { isDarkMode } = useTheme()
  const [timeFilter, setTimeFilter] = useState('Month')
  const [chartType, setChartType] = useState('Orders')
  const [chartView, setChartView] = useState('Line')

  // Sample data for orders with status breakdown
  const orderStats = {
    total: 1247,
    delivered: 892,
    cancelled: 45,
    returned: 23,
    outForDelivery: 156,
    pending: 131
  }

  // Sample data for metrics
  const metrics = {
    totalCategories: 24,
    totalProducts: 1856,
    totalSales: timeFilter === 'Day' ? 45890 : timeFilter === 'Week' ? 245890 : timeFilter === 'Month' ? 1245890 : 14750680
  }

  // Today's orders sample data
  const todaysOrders = [
    { id: 'ORD#1982', customer: 'Amit Sharma', status: 'Out for Delivery', amount: 2349, time: '3 July, 10:15 AM' },
    { id: 'ORD#1981', customer: 'Priya Rao', status: 'Delivered', amount: 1120, time: '3 July, 9:55 AM' },
    { id: 'ORD#1980', customer: 'Rahul Kumar', status: 'Pending', amount: 3456, time: '3 July, 9:30 AM' },
    { id: 'ORD#1979', customer: 'Sneha Patel', status: 'Cancelled', amount: 890, time: '3 July, 8:45 AM' },
    { id: 'ORD#1978', customer: 'Vikash Singh', status: 'Returned', amount: 1567, time: '3 July, 8:20 AM' },
  ]

  // Chart data based on selected type and time filter
  const getChartData = () => {
    const labels = timeFilter === 'Day' ? 
      ['6 AM', '9 AM', '12 PM', '3 PM', '6 PM', '9 PM'] :
      timeFilter === 'Week' ? 
      ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] :
      timeFilter === 'Month' ?
      ['Week 1', 'Week 2', 'Week 3', 'Week 4'] :
      ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    const ordersData = timeFilter === 'Day' ? [45, 67, 89, 123, 156, 98] :
      timeFilter === 'Week' ? [234, 189, 267, 298, 345, 278, 198] :
      timeFilter === 'Month' ? [1234, 1456, 1678, 1345] :
      [2345, 2567, 2234, 2789, 2456, 2890, 2678, 3123, 2945, 3234, 2876, 3456]

    const revenueData = timeFilter === 'Day' ? [12500, 18900, 25600, 34500, 42300, 28900] :
      timeFilter === 'Week' ? [89000, 67000, 95000, 112000, 134000, 98000, 76000] :
      timeFilter === 'Month' ? [456000, 567000, 634000, 523000] :
      [1234000, 1456000, 1234000, 1567000, 1345000, 1678000, 1456000, 1789000, 1567000, 1823000, 1634000, 1945000]

    const returnsData = timeFilter === 'Day' ? [2, 4, 3, 6, 8, 5] :
      timeFilter === 'Week' ? [12, 8, 15, 18, 22, 16, 10] :
      timeFilter === 'Month' ? [45, 67, 78, 56] :
      [123, 145, 134, 167, 145, 178, 156, 189, 167, 198, 176, 212]

    let data, label, color
    if (chartType === 'Orders') {
      data = ordersData
      label = 'Orders'
      color = 'rgb(34, 197, 94)'
    } else if (chartType === 'Revenue') {
      data = revenueData
      label = 'Revenue (₹)'
      color = 'rgb(59, 130, 246)'
    } else {
      data = returnsData
      label = 'Returns'
      color = 'rgb(239, 68, 68)'
    }

    return {
      labels,
      datasets: [{
        label,
        data,
        borderColor: color,
        backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
        fill: chartView === 'Line',
        tension: 0.4,
        pointBackgroundColor: color,
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
      }]
    }
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: isDarkMode ? '#374151' : '#ffffff',
        titleColor: isDarkMode ? '#ffffff' : '#000000',
        bodyColor: isDarkMode ? '#ffffff' : '#000000',
        borderColor: isDarkMode ? '#6b7280' : '#e5e7eb',
        borderWidth: 1,
      },
    },
    scales: {
      x: {
        grid: {
          color: isDarkMode ? '#374151' : '#f3f4f6',
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280',
        },
      },
      y: {
        grid: {
          color: isDarkMode ? '#374151' : '#f3f4f6',
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280',
        },
      },
    },
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Delivered': return 'bg-green-100 text-green-800'
      case 'Cancelled': return 'bg-red-100 text-red-800'
      case 'Returned': return 'bg-orange-100 text-orange-800'
      case 'Out for Delivery': return 'bg-blue-100 text-blue-800'
      case 'Pending': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Delivered': return '✅'
      case 'Cancelled': return '❌'
      case 'Returned': return '🔁'
      case 'Out for Delivery': return '📦'
      case 'Pending': return '⏳'
      default: return '📋'
    }
  }

  return (
    <div className={`p-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Dashboard Overview</h1>
          <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>Welcome back! Here's what's happening with your store today.</p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 mt-4 lg:mt-0">
          {/* Time Filter */}
          <select 
            value={timeFilter} 
            onChange={(e) => setTimeFilter(e.target.value)}
            className={`px-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
              isDarkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="Day">Today</option>
            <option value="Week">This Week</option>
            <option value="Month">This Month</option>
            <option value="Year">This Year</option>
          </select>
          
          {/* Export Button */}
          <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 flex items-center space-x-2">
            <span>📊</span>
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Dashboard Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Orders Card */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm hover:shadow-md transition-shadow duration-200`}>
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl">📋</span>
            </div>
            <select 
              value={timeFilter} 
              onChange={(e) => setTimeFilter(e.target.value)}
              className={`text-xs px-2 py-1 rounded border ${
                isDarkMode ? 'bg-gray-700 border-gray-600 text-gray-300' : 'bg-gray-50 border-gray-300 text-gray-600'
              }`}
            >
              <option value="Day">Day</option>
              <option value="Week">Week</option>
              <option value="Month">Month</option>
              <option value="Year">Year</option>
            </select>
          </div>
          <div>
            <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Orders</p>
            <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-3`}>{orderStats.total.toLocaleString()}</p>
            
            {/* Order Status Breakdown */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="flex items-center space-x-1">
                  <span>✅</span>
                  <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Delivered</span>
                </span>
                <span className="font-medium text-green-600">{orderStats.delivered}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="flex items-center space-x-1">
                  <span>📦</span>
                  <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Out for Delivery</span>
                </span>
                <span className="font-medium text-blue-600">{orderStats.outForDelivery}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="flex items-center space-x-1">
                  <span>⏳</span>
                  <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Pending</span>
                </span>
                <span className="font-medium text-gray-600">{orderStats.pending}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="flex items-center space-x-1">
                  <span>🔁</span>
                  <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Returned</span>
                </span>
                <span className="font-medium text-orange-600">{orderStats.returned}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="flex items-center space-x-1">
                  <span>❌</span>
                  <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Cancelled</span>
                </span>
                <span className="font-medium text-red-600">{orderStats.cancelled}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Total Categories Card */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm hover:shadow-md transition-shadow duration-200`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Categories</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{metrics.totalCategories}</p>
              <p className="text-green-500 text-sm font-medium">+2 this month</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl">🏷️</span>
            </div>
          </div>
        </div>

        {/* Total Products Card */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm hover:shadow-md transition-shadow duration-200`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Products</p>
              <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{metrics.totalProducts.toLocaleString()}</p>
              <p className="text-blue-500 text-sm font-medium">+156 this month</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl">🛍️</span>
            </div>
          </div>
        </div>

        {/* Total Sales Card */}
        <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm hover:shadow-md transition-shadow duration-200`}>
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xl">💰</span>
            </div>
            <select 
              value={timeFilter} 
              onChange={(e) => setTimeFilter(e.target.value)}
              className={`text-xs px-2 py-1 rounded border ${
                isDarkMode ? 'bg-gray-700 border-gray-600 text-gray-300' : 'bg-gray-50 border-gray-300 text-gray-600'
              }`}
            >
              <option value="Day">Day</option>
              <option value="Week">Week</option>
              <option value="Month">Month</option>
              <option value="Year">Year</option>
            </select>
          </div>
          <div>
            <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Net Revenue</p>
            <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>₹{metrics.totalSales.toLocaleString()}</p>
            <p className="text-green-500 text-sm font-medium">+12.5% from last {timeFilter.toLowerCase()}</p>
          </div>
        </div>
      </div>

      {/* Sales/Orders Graph Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 sm:mb-0`}>Sales Analytics</h2>

              <div className="flex flex-col sm:flex-row gap-3">
                {/* Chart Type Toggle */}
                <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                  {['Orders', 'Revenue', 'Returns'].map((type) => (
                    <button
                      key={type}
                      onClick={() => setChartType(type)}
                      className={`px-3 py-1 text-sm rounded-md transition-all duration-200 ${
                        chartType === type
                          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
                          : isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {type}
                    </button>
                  ))}
                </div>

                {/* Chart View Toggle */}
                <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                  {['Line', 'Bar'].map((view) => (
                    <button
                      key={view}
                      onClick={() => setChartView(view)}
                      className={`px-3 py-1 text-sm rounded-md transition-all duration-200 ${
                        chartView === view
                          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
                          : isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {view === 'Line' ? '📈' : '📊'} {view}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="h-80">
              {chartView === 'Line' ? (
                <Line data={getChartData()} options={chartOptions} />
              ) : (
                <Bar data={getChartData()} options={chartOptions} />
              )}
            </div>
          </div>
        </div>

        {/* Quick Stats Sidebar */}
        <div className="space-y-6">
          {/* Quick Analytics Card */}
          <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h4 className="text-sm font-medium mb-1 opacity-90">Today's Performance</h4>
                <p className="text-2xl font-bold">₹65,894</p>
              </div>
              <div className="text-right">
                <p className="text-xs opacity-80">+18.2%</p>
                <p className="text-xs opacity-80">vs yesterday</p>
              </div>
            </div>
            {/* Mini chart */}
            <div className="h-12 flex items-end space-x-1 mt-4">
              {[30, 45, 35, 60, 40, 70, 55, 80, 45, 65].map((height, index) => (
                <div
                  key={index}
                  className="flex-1 bg-white/30 rounded-t"
                  style={{ height: `${height}%` }}
                ></div>
              ))}
            </div>
          </div>

          {/* Low Stock Alert */}
          <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>⚠️ Low Stock Alert</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>iPhone 15 Pro</span>
                <span className="text-red-500 text-sm font-medium">3 left</span>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Samsung Galaxy S24</span>
                <span className="text-orange-500 text-sm font-medium">8 left</span>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>MacBook Air M3</span>
                <span className="text-red-500 text-sm font-medium">2 left</span>
              </div>
            </div>
            <button className="w-full mt-4 px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 text-sm">
              Manage Inventory
            </button>
          </div>
        </div>
      </div>

      {/* Today's Orders Table */}
      <div className={`rounded-xl p-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 sm:mb-0`}>Today's Orders List</h2>

          <div className="flex gap-3">
            <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 text-sm">
              + New Order
            </button>
            <button className={`px-4 py-2 rounded-lg border transition-all duration-200 text-sm ${
              isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-600 hover:bg-gray-50'
            }`}>
              📅 Filter
            </button>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <th className={`text-left py-3 px-4 font-medium text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Order ID</th>
                <th className={`text-left py-3 px-4 font-medium text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Customer Name</th>
                <th className={`text-left py-3 px-4 font-medium text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Status</th>
                <th className={`text-left py-3 px-4 font-medium text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Amount</th>
                <th className={`text-left py-3 px-4 font-medium text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Date & Time</th>
                <th className={`text-left py-3 px-4 font-medium text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {todaysOrders.map((order, index) => (
                <tr key={order.id} className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                  <td className={`py-4 px-4 font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{order.id}</td>
                  <td className={`py-4 px-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{order.customer}</td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      <span>{getStatusIcon(order.status)}</span>
                      <span>{order.status}</span>
                    </span>
                  </td>
                  <td className={`py-4 px-4 font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>₹{order.amount.toLocaleString()}</td>
                  <td className={`py-4 px-4 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{order.time}</td>
                  <td className="py-4 px-4">
                    <div className="flex space-x-2">
                      <button className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors duration-200" title="View">
                        👁️
                      </button>
                      <button className="p-1 text-green-600 hover:bg-green-100 rounded transition-colors duration-200" title="Update">
                        ✏️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Showing 1 to 5 of 47 orders
          </p>
          <div className="flex space-x-2">
            <button className={`px-3 py-1 rounded border text-sm ${isDarkMode ? 'border-gray-600 text-gray-400' : 'border-gray-300 text-gray-600'}`}>
              Previous
            </button>
            <button className="px-3 py-1 rounded bg-gradient-to-r from-green-500 to-green-600 text-white text-sm">
              1
            </button>
            <button className={`px-3 py-1 rounded border text-sm ${isDarkMode ? 'border-gray-600 text-gray-400' : 'border-gray-300 text-gray-600'}`}>
              2
            </button>
            <button className={`px-3 py-1 rounded border text-sm ${isDarkMode ? 'border-gray-600 text-gray-400' : 'border-gray-300 text-gray-600'}`}>
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
