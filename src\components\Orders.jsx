import React, { useState } from 'react'
import { useTheme } from '../contexts/ThemeContext'

function Orders() {
  const { isDarkMode } = useTheme()
  const [filters, setFilters] = useState({
    fromDate: '',
    toDate: '',
    seller: 'All Sellers',
    status: 'All',
    deliveryFromDate: '',
    deliveryToDate: '',
    searchQuery: ''
  })
  const [selectedOrders, setSelectedOrders] = useState([])
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)

  // Sample sellers data
  const sellers = ['All Sellers', 'Daily Groceries', 'Chill Breeze', 'Farm Market', 'Fresh Basket', 'Green Valley']

  // Sample comprehensive orders data with current dates
  const allOrders = [
    {
      id: 7711,
      user: 'Meera',
      seller: 'Daily Groceries',
      mobile: '91*****870',
      total: 320,
      deliveryCharges: 20,
      walletUsed: 50,
      finalTotal: 290,
      paymentMethod: 'UPI',
      deliveryTime: 'Morning 9 AM to 1 PM',
      status: 'Shipped',
      date: '2025-07-09',
      deliveryDate: '2025-07-10'
    },
    {
      id: 7712,
      user: 'Teja',
      seller: 'Chill Breeze',
      mobile: '98*****740',
      total: 180,
      deliveryCharges: 0,
      walletUsed: 0,
      finalTotal: 180,
      paymentMethod: 'COD',
      deliveryTime: 'Evening 6 PM to 9 PM',
      status: 'Delivered',
      date: '2025-07-09',
      deliveryDate: '2025-07-10'
    },
    {
      id: 7713,
      user: 'Aryan',
      seller: 'Farm Market',
      mobile: '97*****320',
      total: 1000,
      deliveryCharges: 30,
      walletUsed: 200,
      finalTotal: 830,
      paymentMethod: 'midtrans',
      deliveryTime: 'Afternoon 2 PM to 6 PM',
      status: 'Returned',
      date: '2025-07-09',
      deliveryDate: '2025-07-09'
    },
    {
      id: 7714,
      user: 'Asha',
      seller: 'Fresh Basket',
      mobile: '90*****123',
      total: 560,
      deliveryCharges: 20,
      walletUsed: 0,
      finalTotal: 580,
      paymentMethod: 'UPI',
      deliveryTime: 'Morning 9 AM to 1 PM',
      status: 'Cancelled',
      date: '2025-07-09',
      deliveryDate: '2025-07-11'
    },
    {
      id: 7715,
      user: 'Rajesh',
      seller: 'Green Valley',
      mobile: '88*****644',
      total: 450,
      deliveryCharges: 0,
      walletUsed: 50,
      finalTotal: 400,
      paymentMethod: 'COD',
      deliveryTime: 'Morning 9 AM to 1 PM',
      status: 'Delivered',
      date: '2025-07-09',
      deliveryDate: '2025-07-12'
    },
    {
      id: 7716,
      user: 'Priya',
      seller: 'Daily Groceries',
      mobile: '95*****234',
      total: 275.50,
      deliveryCharges: 15,
      walletUsed: 25,
      finalTotal: 265.50,
      paymentMethod: 'UPI',
      deliveryTime: 'Evening 6 PM to 9 PM',
      status: 'Shipped',
      date: '2025-07-09',
      deliveryDate: '2025-07-10'
    },
    {
      id: 7717,
      user: 'Vikram',
      seller: 'Farm Market',
      mobile: '92*****567',
      total: 680,
      deliveryCharges: 25,
      walletUsed: 100,
      finalTotal: 605,
      paymentMethod: 'midtrans',
      deliveryTime: 'Afternoon 2 PM to 6 PM',
      status: 'Processed',
      date: '2025-07-08',
      deliveryDate: '2025-07-11'
    },
    {
      id: 7718,
      user: 'Sneha',
      seller: 'Chill Breeze',
      mobile: '89*****890',
      total: 125,
      deliveryCharges: 10,
      walletUsed: 0,
      finalTotal: 135,
      paymentMethod: 'COD',
      deliveryTime: 'Morning 9 AM to 1 PM',
      status: 'Delivered',
      date: '2025-07-08',
      deliveryDate: '2025-07-09'
    },
    {
      id: 7719,
      user: 'Kiran',
      seller: 'Fresh Basket',
      mobile: '94*****456',
      total: 850,
      deliveryCharges: 40,
      walletUsed: 150,
      finalTotal: 740,
      paymentMethod: 'UPI',
      deliveryTime: 'Evening 6 PM to 9 PM',
      status: 'Shipped',
      date: '2025-07-08',
      deliveryDate: '2025-07-12'
    },
    {
      id: 7720,
      user: 'Anita',
      seller: 'Green Valley',
      mobile: '93*****789',
      total: 395.75,
      deliveryCharges: 20,
      walletUsed: 75,
      finalTotal: 340.75,
      paymentMethod: 'COD',
      deliveryTime: 'Afternoon 2 PM to 6 PM',
      status: 'Received',
      date: '2025-07-07',
      deliveryDate: '2025-07-10'
    }
  ]

  // Filter orders based on all criteria
  const filteredOrders = allOrders.filter(order => {
    const orderDate = new Date(order.date)
    const fromDate = new Date(filters.fromDate)
    const toDate = new Date(filters.toDate)

    const matchesDateRange = (!filters.fromDate || orderDate >= fromDate) &&
                            (!filters.toDate || orderDate <= toDate)
    const matchesSeller = filters.seller === 'All Sellers' || order.seller === filters.seller
    const matchesStatus = filters.status === 'All' || order.status === filters.status

    const matchesDeliveryDate = (!filters.deliveryFromDate || new Date(order.deliveryDate) >= new Date(filters.deliveryFromDate)) &&
                               (!filters.deliveryToDate || new Date(order.deliveryDate) <= new Date(filters.deliveryToDate))

    const matchesSearch = !filters.searchQuery ||
                         order.id.toString().includes(filters.searchQuery) ||
                         order.user.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
                         order.mobile.includes(filters.searchQuery)

    return matchesDateRange && matchesSeller && matchesStatus && matchesDeliveryDate && matchesSearch
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedOrders = filteredOrders.slice(startIndex, startIndex + itemsPerPage)

  // Calculate totals for visible orders
  const calculateTotals = () => {
    return paginatedOrders.reduce((acc, order) => ({
      total: acc.total + order.total,
      deliveryCharges: acc.deliveryCharges + order.deliveryCharges,
      walletUsed: acc.walletUsed + order.walletUsed,
      finalTotal: acc.finalTotal + order.finalTotal
    }), { total: 0, deliveryCharges: 0, walletUsed: 0, finalTotal: 0 })
  }

  const totals = calculateTotals()

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1) // Reset to first page when filters change
  }

  // Reset filters
  const resetFilters = () => {
    setFilters({
      fromDate: '',
      toDate: '',
      seller: 'All Sellers',
      status: 'All',
      deliveryFromDate: '',
      deliveryToDate: '',
      searchQuery: ''
    })
    setCurrentPage(1)
  }

  // Handle select all
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedOrders(paginatedOrders.map(order => order.id))
    } else {
      setSelectedOrders([])
    }
  }

  // Handle individual order selection
  const handleOrderSelect = (orderId, checked) => {
    if (checked) {
      setSelectedOrders(prev => [...prev, orderId])
    } else {
      setSelectedOrders(prev => prev.filter(id => id !== orderId))
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Delivered': return 'bg-green-100 text-green-800 border-green-200'
      case 'Cancelled': return 'bg-red-100 text-red-800 border-red-200'
      case 'Returned': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'Shipped': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'Processed': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Received': return 'bg-purple-100 text-purple-800 border-purple-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'Delivered': return 'text-green-600'
      case 'Cancelled': return 'text-red-600'
      case 'Returned': return 'text-orange-600'
      case 'Shipped': return 'text-blue-600'
      case 'Processed': return 'text-yellow-600'
      case 'Received': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="p-6">
        {/* Page Title & Breadcrumb */}
        <div className="mb-6">
          <nav className={`text-sm mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <span>Dashboard</span>
            <span className="mx-2">/</span>
            <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>Order List</span>
          </nav>
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Order List</h1>
        </div>

        {/* Filters Section */}
        <div className={`rounded-xl p-6 mb-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-4">
            {/* Date Range From */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>From Date</label>
              <input
                type="date"
                value={filters.fromDate}
                onChange={(e) => handleFilterChange('fromDate', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* Date Range To */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>To Date</label>
              <input
                type="date"
                value={filters.toDate}
                onChange={(e) => handleFilterChange('toDate', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* Seller Dropdown */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Seller</label>
              <select
                value={filters.seller}
                onChange={(e) => handleFilterChange('seller', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {sellers.map(seller => (
                  <option key={seller} value={seller}>{seller}</option>
                ))}
              </select>
            </div>

            {/* Order Status */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Order Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="All">All</option>
                <option value="Received">Received</option>
                <option value="Processed">Processed</option>
                <option value="Shipped">Shipped</option>
                <option value="Delivered">Delivered</option>
                <option value="Cancelled">Cancelled</option>
                <option value="Returned">Returned</option>
              </select>
            </div>

            {/* Delivery Date From */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Delivery From</label>
              <input
                type="date"
                value={filters.deliveryFromDate}
                onChange={(e) => handleFilterChange('deliveryFromDate', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* Delivery Date To */}
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Delivery To</label>
              <input
                type="date"
                value={filters.deliveryToDate}
                onChange={(e) => handleFilterChange('deliveryToDate', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
          </div>

          {/* Search Bar and Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 items-end">
            <div className="flex-1">
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Search</label>
              <input
                type="text"
                placeholder="Search by Order ID, User name, or Phone number..."
                value={filters.searchQuery}
                onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>

            <div className="flex gap-2">
              <button
                onClick={resetFilters}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-all duration-200 flex items-center space-x-2"
              >
                <span>🗑️</span>
                <span>Clear</span>
              </button>

              <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 flex items-center space-x-2">
                <span>🔄</span>
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedOrders.length > 0 && (
          <div className={`rounded-xl p-4 mb-6 ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm`}>
            <div className="flex items-center justify-between">
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {selectedOrders.length} order(s) selected
              </span>
              <div className="flex gap-2">
                <select className={`px-3 py-1 rounded border text-sm ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}>
                  <option>Bulk Actions</option>
                  <option>Mark as Delivered</option>
                  <option>Mark as Shipped</option>
                  <option>Delete Selected</option>
                </select>
                <button className="px-3 py-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded text-sm">
                  Apply
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Orders Table */}
        <div className={`rounded-xl ${isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'} shadow-sm overflow-hidden`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Latest Orders ({filteredOrders.length} total)
              </h2>
              <div className="flex gap-2">
                <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 text-sm">
                  📊 Export CSV
                </button>
                <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all duration-200 text-sm">
                  📄 Export PDF
                </button>
              </div>
            </div>
          </div>

          {paginatedOrders.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📦</span>
              </div>
              <h3 className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>No orders found</h3>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                No orders match your current filters. Try adjusting your search criteria.
              </p>
            </div>
          ) : (
            <>
              {/* Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <tr>
                      <th className="px-4 py-3 text-left">
                        <input
                          type="checkbox"
                          checked={selectedOrders.length === paginatedOrders.length && paginatedOrders.length > 0}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        O.ID
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        User
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Seller
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Mobile
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Total(₹)
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        D. Charges(₹)
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Wallet Used(₹)
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        F. Total(₹)
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        P. Method
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        D. Time
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Status
                      </th>
                      <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                    {paginatedOrders.map((order) => (
                      <tr key={order.id} className={`hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-colors duration-200`}>
                        <td className="px-4 py-4">
                          <input
                            type="checkbox"
                            checked={selectedOrders.includes(order.id)}
                            onChange={(e) => handleOrderSelect(order.id, e.target.checked)}
                            className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                          />
                        </td>
                        <td className={`px-4 py-4 text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {order.id}
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {order.user}
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {order.seller}
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`} title={order.mobile}>
                          {order.mobile}
                        </td>
                        <td className={`px-4 py-4 text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          ₹{order.total.toLocaleString()}
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          ₹{order.deliveryCharges}
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          ₹{order.walletUsed}
                        </td>
                        <td className={`px-4 py-4 text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          ₹{order.finalTotal.toLocaleString()}
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {order.paymentMethod}
                        </td>
                        <td className={`px-4 py-4 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {order.deliveryTime}
                        </td>
                        <td className="px-4 py-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                            {order.status}
                          </span>
                        </td>
                        <td className="px-4 py-4">
                          <div className="flex space-x-2">
                            <button className="p-1 text-green-600 hover:bg-green-100 rounded transition-colors duration-200" title="View Order">
                              👁️
                            </button>
                            <button className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors duration-200" title="Delete Order">
                              🗑️
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    {/* Totals Row */}
                    <tr className={`border-t-2 font-bold ${isDarkMode ? 'border-gray-600 bg-gray-750' : 'border-gray-300 bg-gray-100'}`}>
                      <td className="px-4 py-4"></td>
                      <td className="px-4 py-4"></td>
                      <td className="px-4 py-4"></td>
                      <td className="px-4 py-4"></td>
                      <td className="px-4 py-4"></td>
                      <td className={`px-4 py-4 text-sm font-bold text-green-600`}>
                        ₹{totals.total.toLocaleString()}
                      </td>
                      <td className={`px-4 py-4 text-sm font-bold text-green-600`}>
                        ₹{totals.deliveryCharges.toLocaleString()}
                      </td>
                      <td className={`px-4 py-4 text-sm font-bold text-green-600`}>
                        ₹{totals.walletUsed.toLocaleString()}
                      </td>
                      <td className={`px-4 py-4 text-sm font-bold text-green-600`}>
                        ₹{totals.finalTotal.toLocaleString()}
                      </td>
                      <td className="px-4 py-4"></td>
                      <td className="px-4 py-4"></td>
                      <td className="px-4 py-4"></td>
                      <td className="px-4 py-4"></td>
                    </tr>
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Per page:</span>
                    <select
                      value={itemsPerPage}
                      onChange={(e) => {
                        setItemsPerPage(Number(e.target.value))
                        setCurrentPage(1)
                      }}
                      className={`px-2 py-1 rounded border text-sm ${
                        isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value={5}>5</option>
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                    </select>
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredOrders.length)} of {filteredOrders.length} orders
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === 1
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      «
                    </button>
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === 1
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      ‹
                    </button>

                    {/* Page Numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else {
                        const start = Math.max(1, currentPage - 2);
                        const end = Math.min(totalPages, start + 4);
                        const adjustedStart = Math.max(1, end - 4);
                        pageNum = adjustedStart + i;
                      }

                      if (pageNum > totalPages) return null;

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`px-3 py-1 rounded text-sm ${
                            currentPage === pageNum
                              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
                              : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      )
                    }).filter(Boolean)}

                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === totalPages
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      ›
                    </button>
                    <button
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className={`px-2 py-1 rounded text-sm ${
                        currentPage === totalPages
                          ? 'text-gray-400 cursor-not-allowed'
                          : isDarkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      »
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default Orders
