# Flexlance User App Setup and Run Script
Write-Host "=== Flexlance User App Setup ===" -ForegroundColor Cyan

# Set environment variables for this session
$env:JAVA_HOME = "C:\Program Files\Android\Android Studio\jbr"
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH = $env:PATH + ";C:\Program Files\Android\Android Studio\jbr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator"

Write-Host "Environment variables set:" -ForegroundColor Green
Write-Host "JAVA_HOME: $env:JAVA_HOME"
Write-Host "ANDROID_HOME: $env:ANDROID_HOME"

# Navigate to project directory
Set-Location "C:\Users\<USER>\Documents\augment-projects\FlexlanceUserApp"
Write-Host "Changed to project directory: $(Get-Location)" -ForegroundColor Yellow

# Check if dependencies are installed
if (!(Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

Write-Host "`n=== Available Commands ===" -ForegroundColor Cyan
Write-Host "1. Start Metro Bundler: npm start"
Write-Host "2. Run on Android: npm run android"
Write-Host "3. List AVDs: emulator -list-avds"
Write-Host "4. Start Emulator: emulator -avd Medium_Phone_API_36.0"
Write-Host "5. Check devices: adb devices"

Write-Host "`n=== Quick Setup Instructions ===" -ForegroundColor Magenta
Write-Host "1. Open a new terminal and run: emulator -avd Medium_Phone_API_36.0"
Write-Host "2. Wait for emulator to fully boot (2-3 minutes)"
Write-Host "3. In another terminal, run: npm start"
Write-Host "4. In a third terminal, run: npm run android"

Write-Host "`nReady to run Flexlance User App!" -ForegroundColor Green
