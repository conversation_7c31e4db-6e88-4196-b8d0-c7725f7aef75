import { useState, useRef, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useTheme } from '../contexts/ThemeContext'

function Layout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [expandedDropdowns, setExpandedDropdowns] = useState({})
  const { user, logout } = useAuth()
  const { isDarkMode, toggleDarkMode } = useTheme()
  const location = useLocation()
  const dropdownRef = useRef(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Toggle navigation dropdown
  const toggleNavDropdown = (itemName) => {
    setExpandedDropdowns(prev => ({
      ...prev,
      [itemName]: !prev[itemName]
    }))
  }

  const navigation = [
    { name: 'Dashboard', href: '/', icon: '📊' },
    { name: 'Orders', href: '/orders', icon: '🛒' },
    { name: 'Categories', href: '/categories', icon: '📂' },
    {
      name: 'Products',
      icon: '📦',
      hasDropdown: true,
      subItems: [
        { name: 'Product List', href: '/products', icon: '📝' },
        { name: 'Create Product', href: '/products/create', icon: '➕' },
        { name: 'Manage Products', href: '/products', icon: '✏️' }
      ]
    },
    { name: 'Promotions', href: '/promotions', icon: '🎯' },
    { name: 'Dispute Resolution', href: '/disputes', icon: '⚖️' },
    { name: 'Financial Reports', href: '/reports', icon: '💰' },
  ]

  const isActive = (href) => {
    return location.pathname === href
  }

  return (
    <div className={`flex h-screen transition-colors duration-200 ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-20'} transition-all duration-300 relative z-10 ${
        isDarkMode ? 'bg-gray-800 border-r border-gray-700' : 'bg-white border-r border-gray-200'
      }`}>
        {/* Logo */}
        <div className={`p-4 ${isDarkMode ? 'border-b border-gray-700' : 'border-b border-gray-200'}`}>
          <div className="flex justify-center">
            <div className="w-24 h-20 flex items-center justify-center overflow-hidden">
              <img
                src="/elitemart-logo.png"
                alt="EliteMart Logo"
                className="w-full h-full object-contain"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'block';
                }}
              />
              <span className="hidden text-4xl">🛒</span>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-6 px-3">
          {navigation.map((item) => (
            <div key={item.name}>
              {item.hasDropdown ? (
                <>
                  {/* Dropdown Parent Item */}
                  <button
                    onClick={() => toggleNavDropdown(item.name)}
                    className={`w-full flex items-center justify-between px-4 py-3 mb-2 rounded-lg transition-all duration-200 ${
                      item.subItems?.some(subItem => isActive(subItem.href))
                        ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="text-lg mr-3">{item.icon}</span>
                      {sidebarOpen && <span className="font-medium">{item.name}</span>}
                    </div>
                    {sidebarOpen && (
                      <span className={`transform transition-transform duration-200 ${
                        expandedDropdowns[item.name] ? 'rotate-180' : ''
                      }`}>
                        ▼
                      </span>
                    )}
                  </button>

                  {/* Dropdown Sub Items */}
                  {expandedDropdowns[item.name] && sidebarOpen && (
                    <div className="ml-4 mb-2 space-y-1">
                      {item.subItems.map((subItem) => (
                        <Link
                          key={subItem.name}
                          to={subItem.href}
                          className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 text-sm ${
                            isActive(subItem.href)
                              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg'
                              : isDarkMode
                                ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                                : 'text-gray-500 hover:bg-gray-100 hover:text-gray-900'
                          }`}
                        >
                          <span className="text-base mr-3">{subItem.icon}</span>
                          <span className="font-medium">{subItem.name}</span>
                        </Link>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                /* Regular Navigation Item */
                <Link
                  to={item.href}
                  className={`flex items-center px-4 py-3 mb-2 rounded-lg transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg'
                      : isDarkMode
                        ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <span className="text-lg mr-3">{item.icon}</span>
                  {sidebarOpen && <span className="font-medium">{item.name}</span>}
                </Link>
              )}
            </div>
          ))}
        </nav>


      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className={`px-6 py-4 ${
          isDarkMode
            ? 'bg-gray-800 border-b border-gray-700'
            : 'bg-white border-b border-gray-200'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <svg className={`w-6 h-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Dashboard</h1>
            </div>

            <div className="flex items-center space-x-6">
              {/* Search */}
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  className={`px-4 py-2 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors duration-200 ${
                    isDarkMode
                      ? 'bg-gray-700 text-white placeholder-gray-400'
                      : 'bg-gray-100 text-gray-900 placeholder-gray-500'
                  }`}
                />
                <span className={`absolute right-3 top-2.5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>🔍</span>
              </div>

              {/* Night Mode Toggle */}
              <div className="flex items-center space-x-2">
                <span className="text-sm">{isDarkMode ? '🌙' : '☀️'}</span>
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Night Mode</span>
                <button
                  onClick={toggleDarkMode}
                  className={`w-10 h-6 rounded-full relative transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                    isDarkMode ? 'bg-purple-500' : 'bg-gray-300'
                  }`}
                >
                  <div
                    className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform duration-200 ${
                      isDarkMode ? 'translate-x-4' : 'translate-x-0'
                    }`}
                  ></div>
                </button>
              </div>

              {/* User Profile Dropdown */}
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                  className="flex items-center space-x-3 focus:outline-none"
                >
                  <div className="text-right">
                    <p className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Adele Mitchell</p>
                    <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>super admin</p>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">AM</span>
                  </div>
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''} ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Dropdown Menu */}
                {dropdownOpen && (
                  <div className={`absolute right-0 mt-2 w-48 rounded-lg shadow-lg z-50 ${
                    isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
                  }`}>
                    <div className="py-2">
                      <button
                        className={`w-full text-left px-4 py-2 text-sm transition-colors duration-200 flex items-center space-x-3 ${
                          isDarkMode ? 'text-gray-300 hover:bg-green-600 hover:text-white' : 'text-gray-700 hover:bg-green-500 hover:text-white'
                        }`}
                      >
                        <span className="text-lg">👤</span>
                        <span>My Profile</span>
                      </button>

                      <button
                        className={`w-full text-left px-4 py-2 text-sm transition-colors duration-200 flex items-center space-x-3 ${
                          isDarkMode ? 'text-gray-300 hover:bg-green-600 hover:text-white' : 'text-gray-700 hover:bg-green-500 hover:text-white'
                        }`}
                      >
                        <span className="text-lg">⚙️</span>
                        <span>Settings</span>
                      </button>

                      <div className={`border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} my-1`}></div>

                      <button
                        onClick={logout}
                        className={`w-full text-left px-4 py-2 text-sm transition-all duration-200 flex items-center space-x-3 hover:bg-gradient-to-r hover:from-green-500 hover:to-green-600 hover:text-white rounded-md ${
                          isDarkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}
                      >
                        <span className="text-lg">🚪</span>
                        <span>Logout</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className={`flex-1 overflow-y-auto transition-colors duration-200 ${
          isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
        }`}>
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout
